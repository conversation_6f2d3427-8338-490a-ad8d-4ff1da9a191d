<?php

use yii\db\Migration;

/**
 * Fixes the notification_user table to match the model expectations
 */
class m250923_000002_fix_notification_user_table extends Migration
{
    public function safeUp()
    {
        // Check if the table exists with the old name
        $tableExists = $this->db->schema->getTableSchema('user_notification') !== null;
        $newTableExists = $this->db->schema->getTableSchema('notification_user') !== null;
        
        if ($tableExists && !$newTableExists) {
            // Rename the table to match what the model expects
            $this->renameTable('{{%user_notification}}', '{{%notification_user}}');
            echo "Renamed table from user_notification to notification_user.\n";
        } elseif (!$newTableExists) {
            // Create the table if it doesn't exist at all
            $this->createTable('{{%notification_user}}', [
                'id' => $this->primaryKey(),
                'notification_id' => $this->integer()->notNull(),
                'user_id' => $this->integer()->notNull(),
                'message' => $this->text()->notNull(),
                'is_read' => $this->boolean()->defaultValue(false),
                'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            ]);
            
            $this->addForeignKey('fk_notification_user_notification', '{{%notification_user}}', 'notification_id', '{{%notification}}', 'id', 'CASCADE');
            $this->addForeignKey('fk_notification_user_user', '{{%notification_user}}', 'user_id', '{{%user}}', 'id', 'CASCADE');
            echo "Created notification_user table.\n";
        }
        
        // Add the link field that NotificationManager expects
        $schema = $this->db->schema->getTableSchema('notification_user');
        if ($schema && !isset($schema->columns['link'])) {
            $this->addColumn('{{%notification_user}}', 'link', $this->string()->null());
            echo "Added link column to notification_user table.\n";
        }
    }

    public function safeDown()
    {
        // Remove the link column
        $schema = $this->db->schema->getTableSchema('notification_user');
        if ($schema && isset($schema->columns['link'])) {
            $this->dropColumn('{{%notification_user}}', 'link');
        }
        
        // Rename back to original name if it was renamed
        $tableExists = $this->db->schema->getTableSchema('notification_user') !== null;
        $oldTableExists = $this->db->schema->getTableSchema('user_notification') !== null;
        
        if ($tableExists && !$oldTableExists) {
            $this->renameTable('{{%notification_user}}', '{{%user_notification}}');
            echo "Renamed table back to user_notification.\n";
        }
    }
}
