a:14:{s:6:"config";s:15911:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:71:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:34:"miloschuman/yii2-highcharts-widget";a:3:{s:4:"name";s:34:"miloschuman/yii2-highcharts-widget";s:7:"version";s:8:"11.0.0.0";s:5:"alias";a:1:{s:23:"@miloschuman/highcharts";s:65:"C:\Web\Reclassering\vendor/miloschuman/yii2-highcharts-widget/src";}}}}";s:3:"log";s:36770:"a:1:{s:8:"messages";a:62:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.182344;i:4;a:0:{}i:5;i:3011056;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.182977;i:4;a:0:{}i:5;i:3116248;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.182984;i:4;a:0:{}i:5;i:3116544;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.183281;i:4;a:0:{}i:5;i:3145904;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.183609;i:4;a:0:{}i:5;i:3174016;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.185832;i:4;a:0:{}i:5;i:3394584;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.18589;i:4;a:0:{}i:5;i:3395224;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.190959;i:4;a:0:{}i:5;i:4295064;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.205509;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5619160;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.205602;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5686976;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.210143;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5744792;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.211051;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5765512;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.217906;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6169792;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.238503;i:4;a:0:{}i:5;i:6806744;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.241149;i:4;a:0:{}i:5;i:6986840;}i:25;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.241164;i:4;a:0:{}i:5;i:6987480;}i:26;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.243448;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7057400;}i:29;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.245512;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7069032;}i:32;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.246422;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7068816;}i:35;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.252599;i:4;a:0:{}i:5;i:7249208;}i:37;a:6:{i:0;s:36:"Route requested: 'notification-role'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.252669;i:4;a:0:{}i:5;i:7313680;}i:38;a:6:{i:0;s:37:"Route to run: notification-role/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.255055;i:4;a:0:{}i:5;i:7385056;}i:39;a:6:{i:0;s:169:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('notification-role/index', '1', '::1', 0, 'GET', '2025-09-25 13:36:38')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.264565;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7656584;}i:42;a:6:{i:0;s:77:"SELECT * FROM `notification_trigger` WHERE `route`='/notification-role/index'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.271389;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7719992;}i:45;a:6:{i:0;s:77:"Running action: backend\controllers\NotificationRoleController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.272349;i:4;a:0:{}i:5;i:7714208;}i:46;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.27416;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7905568;}i:49;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.276062;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7911920;}i:52;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_role' AND `kcu`.`TABLE_NAME` = 'notification_role'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.276883;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7916184;}i:55;a:6:{i:0;s:82:"Rendering view file: C:\Web\Reclassering\backend\views\notification-role\index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.280891;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:8372352;}i:56;a:6:{i:0;s:24:"Loading module: gridview";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.325256;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-krajee-base\src\Config.php";s:4:"line";i:313;s:8:"function";s:9:"getModule";s:5:"class";s:15:"yii\base\Module";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1255;s:8:"function";s:9:"getModule";s:5:"class";s:18:"kartik\base\Config";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:824;s:8:"function";s:10:"initModule";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:9758400;}i:57;a:6:{i:0;s:40:"SELECT COUNT(*) FROM `notification_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.353404;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10522248;}i:60;a:6:{i:0;s:42:"SELECT * FROM `notification_role` LIMIT 20";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.38464;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\backend\views\notification-role\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11107816;}i:63;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.39327;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11333168;}i:66;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.394435;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11342296;}i:69;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.396904;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11354360;}i:72;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.398544;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11360976;}i:75;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.402328;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11393800;}i:78;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.404456;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11416088;}i:81;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.40708;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11429176;}i:84;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.408296;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11431520;}i:87;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=2";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.412024;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11439008;}i:90;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.413099;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11446568;}i:93;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.414969;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11460952;}i:96;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.416067;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11468496;}i:99;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=3";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.418045;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11482888;}i:102;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.419451;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11490448;}i:105;a:6:{i:0;s:71:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.427584;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11531312;}i:106;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\navbar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.447913;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11641224;}i:107;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.45119;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11719640;}i:110;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.452355;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11724536;}i:113;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.453099;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11733864;}i:116;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.45523;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11745880;}i:119;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.456044;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11748784;}i:122;a:6:{i:0;s:79:"Rendering view file: C:\Web\Reclassering\common\widgets\views\notifications.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.458237;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:27;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11750392;}i:123;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\profile.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.461297;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\ProfileWidget.php";s:4:"line";i:32;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:26;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11760624;}i:124;a:6:{i:0;s:74:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.462612;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:49;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11725912;}i:125;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.464467;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:49;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11771944;}i:126;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.469604;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11925408;}i:129;a:6:{i:0;s:74:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\content.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.474088;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:52;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11965880;}i:130;a:6:{i:0;s:82:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\control-sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.477903;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:56;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:12021952;}i:131;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\footer.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.478871;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:44;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:12024272;}i:132;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.30911183357239, `memory_max`=12232728 WHERE `id`=5351";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.484177;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:12036080;}}}";s:9:"profiling";s:55620:"a:3:{s:6:"memory";i:12232728;s:4:"time";d:0.3161919116973877;s:8:"messages";a:72:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.205639;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5687784;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.208668;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5731088;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.208685;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5730872;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.21011;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5743504;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.210154;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5745704;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.210563;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5748280;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.211066;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5766552;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.212335;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5769080;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.217951;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6170176;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.218734;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6172544;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.243514;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7058312;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.245479;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7067744;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.245523;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7069944;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.246049;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7071856;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.246471;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7070496;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.247879;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7072352;}i:40;a:6:{i:0;s:169:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('notification-role/index', '1', '::1', 0, 'GET', '2025-09-25 13:36:38')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.264616;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7657944;}i:41;a:6:{i:0;s:169:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('notification-role/index', '1', '::1', 0, 'GET', '2025-09-25 13:36:38')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.268744;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7659736;}i:43;a:6:{i:0;s:77:"SELECT * FROM `notification_trigger` WHERE `route`='/notification-role/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.271427;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7721496;}i:44;a:6:{i:0;s:77:"SELECT * FROM `notification_trigger` WHERE `route`='/notification-role/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.2723;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7722976;}i:47;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.274215;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7906856;}i:48;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.276027;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7910248;}i:50;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.27608;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7913208;}i:51;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.276806;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7915504;}i:53;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_role' AND `kcu`.`TABLE_NAME` = 'notification_role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.276897;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7917600;}i:54;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_role' AND `kcu`.`TABLE_NAME` = 'notification_role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.278779;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7921072;}i:58;a:6:{i:0;s:40:"SELECT COUNT(*) FROM `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.353496;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10523912;}i:59;a:6:{i:0;s:40:"SELECT COUNT(*) FROM `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.35409;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10525536;}i:61;a:6:{i:0;s:42:"SELECT * FROM `notification_role` LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.384704;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\backend\views\notification-role\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11109480;}i:62;a:6:{i:0;s:42:"SELECT * FROM `notification_role` LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.38553;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\backend\views\notification-role\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11112808;}i:64;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393352;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11335048;}i:65;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.394315;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11338704;}i:67;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.394462;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11343960;}i:68;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.396829;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11352312;}i:70;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.396933;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11356024;}i:71;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.397972;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11358560;}i:73;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.398584;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11362768;}i:74;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.400549;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11365376;}i:76;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.402393;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11395680;}i:77;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.403394;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11398088;}i:79;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.404519;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11417752;}i:80;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.407003;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11427136;}i:82;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.407108;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11430840;}i:83;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.408083;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11433752;}i:85;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.408338;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11433312;}i:86;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.410661;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11437136;}i:88;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=2";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.412078;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11440888;}i:89;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=2";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.412787;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11443280;}i:91;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.413125;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11448448;}i:92;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.413857;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11450776;}i:94;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.415028;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11462832;}i:95;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.415765;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11465208;}i:97;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.416093;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11470376;}i:98;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.416669;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11472712;}i:100;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.418095;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11484768;}i:101;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.418873;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11487160;}i:103;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.41951;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11492328;}i:104;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.420209;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11494664;}i:108;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.451256;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11721416;}i:109;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.452221;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11723112;}i:111;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.452374;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11726416;}i:112;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.45302;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11729552;}i:114;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.453129;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11734888;}i:115;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.455191;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11743192;}i:117;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.455244;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11747544;}i:118;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.455929;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11750344;}i:120;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.456059;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11749936;}i:121;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.457591;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11753784;}i:127;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.469684;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11927080;}i:128;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.470808;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11959688;}i:133;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.30911183357239, `memory_max`=12232728 WHERE `id`=5351";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.484196;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:12037424;}i:134;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.30911183357239, `memory_max`=12232728 WHERE `id`=5351";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.487714;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:12038824;}}}";s:2:"db";s:54850:"a:1:{s:8:"messages";a:70:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.208685;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5730872;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.21011;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5743504;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.210154;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5745704;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.210563;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5748280;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.211066;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5766552;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.212335;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5769080;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.217951;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6170176;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.218734;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6172544;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.243514;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7058312;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.245479;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7067744;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.245523;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7069944;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.246049;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7071856;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.246471;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7070496;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.247879;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7072352;}i:40;a:6:{i:0;s:169:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('notification-role/index', '1', '::1', 0, 'GET', '2025-09-25 13:36:38')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.264616;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7657944;}i:41;a:6:{i:0;s:169:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('notification-role/index', '1', '::1', 0, 'GET', '2025-09-25 13:36:38')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.268744;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7659736;}i:43;a:6:{i:0;s:77:"SELECT * FROM `notification_trigger` WHERE `route`='/notification-role/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.271427;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7721496;}i:44;a:6:{i:0;s:77:"SELECT * FROM `notification_trigger` WHERE `route`='/notification-role/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.2723;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7722976;}i:47;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.274215;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7906856;}i:48;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.276027;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7910248;}i:50;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.27608;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7913208;}i:51;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.276806;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7915504;}i:53;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_role' AND `kcu`.`TABLE_NAME` = 'notification_role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.276897;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7917600;}i:54;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_role' AND `kcu`.`TABLE_NAME` = 'notification_role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.278779;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\common\models\search\NotificationRoleSearch.php";s:4:"line";i:51;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\NotificationRoleController.php";s:4:"line";i:42;s:8:"function";s:6:"search";s:5:"class";s:43:"common\models\search\NotificationRoleSearch";s:4:"type";s:2:"->";}}i:5;i:7921072;}i:58;a:6:{i:0;s:40:"SELECT COUNT(*) FROM `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.353496;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10523912;}i:59;a:6:{i:0;s:40:"SELECT COUNT(*) FROM `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.35409;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10525536;}i:61;a:6:{i:0;s:42:"SELECT * FROM `notification_role` LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.384704;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\backend\views\notification-role\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11109480;}i:62;a:6:{i:0;s:42:"SELECT * FROM `notification_role` LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.38553;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\backend\views\notification-role\index.php";s:4:"line";i:22;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11112808;}i:64;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393352;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11335048;}i:65;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.394315;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11338704;}i:67;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.394462;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11343960;}i:68;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.396829;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11352312;}i:70;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.396933;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11356024;}i:71;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.397972;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11358560;}i:73;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.398584;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11362768;}i:74;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.400549;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11365376;}i:76;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.402393;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11395680;}i:77;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.403394;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11398088;}i:79;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.404519;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11417752;}i:80;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.407003;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11427136;}i:82;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.407108;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11430840;}i:83;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.408083;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11433752;}i:85;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.408338;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11433312;}i:86;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.410661;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11437136;}i:88;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=2";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.412078;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11440888;}i:89;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=2";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.412787;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11443280;}i:91;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.413125;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11448448;}i:92;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.413857;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11450776;}i:94;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.415028;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11462832;}i:95;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.415765;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11465208;}i:97;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.416093;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11470376;}i:98;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.416669;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11472712;}i:100;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.418095;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11484768;}i:101;a:6:{i:0;s:41:"SELECT * FROM `notification` WHERE `id`=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.418873;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11487160;}i:103;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.41951;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11492328;}i:104;a:6:{i:0;s:33:"SELECT * FROM `role` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.420209;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\DataColumn.php";s:4:"line";i:248;s:8:"function";s:21:"renderDataCellContent";s:5:"class";s:19:"yii\grid\DataColumn";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1112;s:8:"function";s:14:"renderDataCell";s:5:"class";s:22:"kartik\grid\DataColumn";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:125;s:8:"function";s:15:"renderTableBody";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:11494664;}i:108;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.451256;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11721416;}i:109;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.452221;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11723112;}i:111;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.452374;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11726416;}i:112;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.45302;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11729552;}i:114;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.453129;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11734888;}i:115;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.455191;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11743192;}i:117;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.455244;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11747544;}i:118;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.455929;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11750344;}i:120;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.456059;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11749936;}i:121;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.457591;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:24;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11753784;}i:127;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.469684;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11927080;}i:128;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.470808;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11959688;}i:133;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.30911183357239, `memory_max`=12232728 WHERE `id`=5351";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.484196;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:12037424;}i:134;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.30911183357239, `memory_max`=12232728 WHERE `id`=5351";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.487714;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:12038824;}}}";s:5:"event";s:34224:"a:184:{i:0;a:5:{s:4:"time";d:**********.198928;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:**********.208662;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:**********.220071;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:**********.220399;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:**********.231773;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:**********.252643;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:**********.258785;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:7;a:5:{s:4:"time";d:**********.258856;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:8;a:5:{s:4:"time";d:**********.263339;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:9;a:5:{s:4:"time";d:**********.269134;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:10;a:5:{s:4:"time";d:**********.269159;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:**********.269593;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:46:"backend\controllers\NotificationRoleController";}i:12;a:5:{s:4:"time";d:**********.271229;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:13;a:5:{s:4:"time";d:**********.272927;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:43:"common\models\search\NotificationRoleSearch";}i:14;a:5:{s:4:"time";d:**********.272949;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:15;a:5:{s:4:"time";d:**********.273666;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:43:"common\models\search\NotificationRoleSearch";}i:16;a:5:{s:4:"time";d:**********.278821;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:43:"common\models\search\NotificationRoleSearch";}i:17;a:5:{s:4:"time";d:**********.280878;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:18;a:5:{s:4:"time";d:**********.322267;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:19;a:5:{s:4:"time";d:**********.32249;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:20;a:5:{s:4:"time";d:**********.3225;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:21;a:5:{s:4:"time";d:**********.330284;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:22;a:5:{s:4:"time";d:**********.33034;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:23;a:5:{s:4:"time";d:**********.330387;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:24;a:5:{s:4:"time";d:**********.330474;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:25;a:5:{s:4:"time";d:**********.3519;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:26;a:5:{s:4:"time";d:**********.351955;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:27;a:5:{s:4:"time";d:**********.351971;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:28;a:5:{s:4:"time";d:**********.351982;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:29;a:5:{s:4:"time";d:**********.352026;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:30;a:5:{s:4:"time";d:**********.354131;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:31;a:5:{s:4:"time";d:**********.354147;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:32;a:5:{s:4:"time";d:**********.354152;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:33;a:5:{s:4:"time";d:**********.354159;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:34;a:5:{s:4:"time";d:**********.354166;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:35;a:5:{s:4:"time";d:**********.354185;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:36;a:5:{s:4:"time";d:**********.354193;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:37;a:5:{s:4:"time";d:**********.354204;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:38;a:5:{s:4:"time";d:**********.35422;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:39;a:5:{s:4:"time";d:**********.354227;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:40;a:5:{s:4:"time";d:**********.354234;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:41;a:5:{s:4:"time";d:**********.354242;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:42;a:5:{s:4:"time";d:**********.354251;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:43;a:5:{s:4:"time";d:**********.354259;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:44;a:5:{s:4:"time";d:**********.354267;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:45;a:5:{s:4:"time";d:**********.354274;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:46;a:5:{s:4:"time";d:**********.354281;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:47;a:5:{s:4:"time";d:**********.354296;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:48;a:5:{s:4:"time";d:**********.354305;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:49;a:5:{s:4:"time";d:**********.354312;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:50;a:5:{s:4:"time";d:**********.354319;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:51;a:5:{s:4:"time";d:**********.354326;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:52;a:5:{s:4:"time";d:**********.354333;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:53;a:5:{s:4:"time";d:**********.354342;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:54;a:5:{s:4:"time";d:**********.354349;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:55;a:5:{s:4:"time";d:**********.354356;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:56;a:5:{s:4:"time";d:**********.354364;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:57;a:5:{s:4:"time";d:**********.354371;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:58;a:5:{s:4:"time";d:**********.354377;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:59;a:5:{s:4:"time";d:**********.354386;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:60;a:5:{s:4:"time";d:**********.354393;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:61;a:5:{s:4:"time";d:**********.354399;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:62;a:5:{s:4:"time";d:**********.354406;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:63;a:5:{s:4:"time";d:**********.354415;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:64;a:5:{s:4:"time";d:**********.354421;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:65;a:5:{s:4:"time";d:**********.35443;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:66;a:5:{s:4:"time";d:**********.354437;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:67;a:5:{s:4:"time";d:**********.354444;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:68;a:5:{s:4:"time";d:**********.354452;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:69;a:5:{s:4:"time";d:**********.354459;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:70;a:5:{s:4:"time";d:**********.358943;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:71;a:5:{s:4:"time";d:**********.358984;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:72;a:5:{s:4:"time";d:**********.362434;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:73;a:5:{s:4:"time";d:**********.362489;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:74;a:5:{s:4:"time";d:**********.362594;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:75;a:5:{s:4:"time";d:**********.3654;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:76;a:5:{s:4:"time";d:**********.365455;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:77;a:5:{s:4:"time";d:**********.369684;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:78;a:5:{s:4:"time";d:**********.369749;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:79;a:5:{s:4:"time";d:**********.374114;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:80;a:5:{s:4:"time";d:**********.374164;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:81;a:5:{s:4:"time";d:**********.375955;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:82;a:5:{s:4:"time";d:**********.376001;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:83;a:5:{s:4:"time";d:**********.376032;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:84;a:5:{s:4:"time";d:**********.376072;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:85;a:5:{s:4:"time";d:**********.3783;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:86;a:5:{s:4:"time";d:**********.382389;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:87;a:5:{s:4:"time";d:**********.384431;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationRole";}i:88;a:5:{s:4:"time";d:**********.385654;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationRole";}i:89;a:5:{s:4:"time";d:**********.385701;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationRole";}i:90;a:5:{s:4:"time";d:**********.385715;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationRole";}i:91;a:5:{s:4:"time";d:**********.385726;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationRole";}i:92;a:5:{s:4:"time";d:**********.385738;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationRole";}i:93;a:5:{s:4:"time";d:**********.385744;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationRole";}i:94;a:5:{s:4:"time";d:**********.385749;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationRole";}i:95;a:5:{s:4:"time";d:**********.385754;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationRole";}i:96;a:5:{s:4:"time";d:**********.385821;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:97;a:5:{s:4:"time";d:**********.390413;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:98;a:5:{s:4:"time";d:**********.394372;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Notification";}i:99;a:5:{s:4:"time";d:**********.400656;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Notification";}i:100;a:5:{s:4:"time";d:**********.402111;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:101;a:5:{s:4:"time";d:**********.404363;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:102;a:5:{s:4:"time";d:**********.410842;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:103;a:5:{s:4:"time";d:**********.411331;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:104;a:5:{s:4:"time";d:**********.411816;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:105;a:5:{s:4:"time";d:**********.412848;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Notification";}i:106;a:5:{s:4:"time";d:**********.412892;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Notification";}i:107;a:5:{s:4:"time";d:**********.412995;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:108;a:5:{s:4:"time";d:**********.413987;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:109;a:5:{s:4:"time";d:**********.41406;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:110;a:5:{s:4:"time";d:**********.414795;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:111;a:5:{s:4:"time";d:**********.41582;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Notification";}i:112;a:5:{s:4:"time";d:**********.415863;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Notification";}i:113;a:5:{s:4:"time";d:**********.415963;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:114;a:5:{s:4:"time";d:**********.416804;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:115;a:5:{s:4:"time";d:**********.416874;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:116;a:5:{s:4:"time";d:**********.417868;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:117;a:5:{s:4:"time";d:**********.419012;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Notification";}i:118;a:5:{s:4:"time";d:**********.419087;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Notification";}i:119;a:5:{s:4:"time";d:**********.419263;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:120;a:5:{s:4:"time";d:**********.420303;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:121;a:5:{s:4:"time";d:**********.420342;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:122;a:5:{s:4:"time";d:**********.421581;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:123;a:5:{s:4:"time";d:**********.421596;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:124;a:5:{s:4:"time";d:**********.421609;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:125;a:5:{s:4:"time";d:**********.421673;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:126;a:5:{s:4:"time";d:**********.424014;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:127;a:5:{s:4:"time";d:**********.424048;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:128;a:5:{s:4:"time";d:**********.425673;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:129;a:5:{s:4:"time";d:**********.427104;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:130;a:5:{s:4:"time";d:**********.427209;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:131;a:5:{s:4:"time";d:**********.427269;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:132;a:5:{s:4:"time";d:**********.427572;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:133;a:5:{s:4:"time";d:**********.447393;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:134;a:5:{s:4:"time";d:**********.447615;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:135;a:5:{s:4:"time";d:**********.447898;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:136;a:5:{s:4:"time";d:**********.449857;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:137;a:5:{s:4:"time";d:**********.449884;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:138;a:5:{s:4:"time";d:**********.450983;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:139;a:5:{s:4:"time";d:**********.45227;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:140;a:5:{s:4:"time";d:**********.453057;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:141;a:5:{s:4:"time";d:**********.457698;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:142;a:5:{s:4:"time";d:**********.457755;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:143;a:5:{s:4:"time";d:**********.457773;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:144;a:5:{s:4:"time";d:**********.458223;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:145;a:5:{s:4:"time";d:**********.459848;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:146;a:5:{s:4:"time";d:**********.459881;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:147;a:5:{s:4:"time";d:**********.460965;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:148;a:5:{s:4:"time";d:**********.460987;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:149;a:5:{s:4:"time";d:**********.461284;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:150;a:5:{s:4:"time";d:**********.462245;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:151;a:5:{s:4:"time";d:**********.462273;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:152;a:5:{s:4:"time";d:**********.462339;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:153;a:5:{s:4:"time";d:**********.462601;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:154;a:5:{s:4:"time";d:**********.464154;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:155;a:5:{s:4:"time";d:**********.464176;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:156;a:5:{s:4:"time";d:**********.464454;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:157;a:5:{s:4:"time";d:**********.466796;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:158;a:5:{s:4:"time";d:**********.472288;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:159;a:5:{s:4:"time";d:**********.472304;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:160;a:5:{s:4:"time";d:**********.473484;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:161;a:5:{s:4:"time";d:**********.473585;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:162;a:5:{s:4:"time";d:**********.473609;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:163;a:5:{s:4:"time";d:**********.473648;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:164;a:5:{s:4:"time";d:**********.474076;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:165;a:5:{s:4:"time";d:**********.47731;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:166;a:5:{s:4:"time";d:**********.47734;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:167;a:5:{s:4:"time";d:**********.47751;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:168;a:5:{s:4:"time";d:**********.47757;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:169;a:5:{s:4:"time";d:**********.477892;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:170;a:5:{s:4:"time";d:**********.478618;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:171;a:5:{s:4:"time";d:**********.478859;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:172;a:5:{s:4:"time";d:**********.479542;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:173;a:5:{s:4:"time";d:**********.482032;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:174;a:5:{s:4:"time";d:**********.483456;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:175;a:5:{s:4:"time";d:**********.484006;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:176;a:5:{s:4:"time";d:**********.484021;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:46:"backend\controllers\NotificationRoleController";}i:177;a:5:{s:4:"time";d:**********.48403;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:178;a:5:{s:4:"time";d:**********.484084;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:179;a:5:{s:4:"time";d:**********.4878;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:180;a:5:{s:4:"time";d:**********.487826;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:181;a:5:{s:4:"time";d:**********.487877;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:182;a:5:{s:4:"time";d:**********.489313;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:183;a:5:{s:4:"time";d:**********.489962;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.174936;s:3:"end";d:**********.49155;s:6:"memory";i:12232728;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:331:"a:3:{s:8:"messages";a:1:{i:36;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.252656;i:4;a:0:{}i:5;i:7313776;}}s:5:"route";s:23:"notification-role/index";s:6:"action";s:61:"backend\controllers\NotificationRoleController::actionIndex()";}";s:7:"request";s:11017:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:18:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36";s:7:"referer";s:57:"http://localhost:8005/backoffice/index.php?r=notification";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:957:"sidebar-collapse=false; advanced-frontend-fmz=5aq56c0k9mq2kuj625o5s3h214; _identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; _csrf-frontend=2d4c3047e7a91b1f2885c3537d6571c92c52456f58a3a57ccf3a69a5a789f51ba%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22BXnguNFHn_7mQloD5FR82zMnsqS7ncXT%22%3B%7D; advanced-backend-fmz=jvvvm6hursbt8umpglia4j5spl; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; _csrf-backend=67baeba99e419a42546f2a459a8d5d32745bb89561fdf16aa322cedafd10ea5aa%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22NPiQ_az7UT76sP4u5MsO_ELXpE8sDInM%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68d537563a8a3";s:16:"X-Debug-Duration";s:3:"315";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=68d537563a8a3";s:10:"Set-Cookie";s:311:"_identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; expires=Sat, 25 Oct 2025 12:36:38 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:23:"notification-role/index";s:6:"action";s:61:"backend\controllers\NotificationRoleController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:104:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-3e6a14b4-4502-4709-a8d7-e00ac4bfe81d";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:582:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\GitHub CLI\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:41:"/backoffice/index.php?r=notification-role";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"49198";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:19:"r=notification-role";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36";s:12:"HTTP_REFERER";s:57:"http://localhost:8005/backoffice/index.php?r=notification";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:957:"sidebar-collapse=false; advanced-frontend-fmz=5aq56c0k9mq2kuj625o5s3h214; _identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; _csrf-frontend=2d4c3047e7a91b1f2885c3537d6571c92c52456f58a3a57ccf3a69a5a789f51ba%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22BXnguNFHn_7mQloD5FR82zMnsqS7ncXT%22%3B%7D; advanced-backend-fmz=jvvvm6hursbt8umpglia4j5spl; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; _csrf-backend=67baeba99e419a42546f2a459a8d5d32745bb89561fdf16aa322cedafd10ea5aa%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22NPiQ_az7UT76sP4u5MsO_ELXpE8sDInM%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.161808;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:1:{s:1:"r";s:17:"notification-role";}s:4:"POST";a:0:{}s:6:"COOKIE";a:7:{s:16:"sidebar-collapse";s:5:"false";s:21:"advanced-frontend-fmz";s:26:"5aq56c0k9mq2kuj625o5s3h214";s:18:"_identity-frontend";s:158:"ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:14:"_csrf-frontend";s:140:"2d4c3047e7a91b1f2885c3537d6571c92c52456f58a3a57ccf3a69a5a789f51ba:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"BXnguNFHn_7mQloD5FR82zMnsqS7ncXT";}";s:20:"advanced-backend-fmz";s:26:"jvvvm6hursbt8umpglia4j5spl";s:17:"_identity-backend";s:157:"39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:13:"_csrf-backend";s:139:"67baeba99e419a42546f2a459a8d5d32745bb89561fdf16aa322cedafd10ea5aa:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"NPiQ_az7UT76sP4u5MsO_ELXpE8sDInM";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:4:{s:7:"__flash";a:0:{}s:11:"__returnUrl";s:33:"http://localhost:8005/backoffice/";s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"**********";s:10:"updated_at";s:10:"**********";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_**********'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:14836:"a:27:{s:31:"yii2ajaxcrud\ajaxcrud\CrudAsset";a:9:{s:10:"sourcePath";s:64:"C:\Web\Reclassering\vendor\biladina\yii2-ajaxcrud-bs4\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a6a94290";s:7:"baseUrl";s:27:"/backoffice/assets/a6a94290";s:7:"depends";a:5:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:35:"yii\bootstrap5\BootstrapPluginAsset";i:3;s:25:"kartik\grid\GridViewAsset";i:4;s:38:"yii2ajaxcrud\ajaxcrud\FontAwesomeAsset";}s:2:"js";a:2:{i:0;s:14:"ModalRemote.js";i:1;s:11:"ajaxcrud.js";}s:3:"css";a:1:{i:0;s:12:"ajaxcrud.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:50:"C:\Web\Reclassering/vendor/bower-asset/jquery/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\8bc86ca8";s:7:"baseUrl";s:27:"/backoffice/assets/8bc86ca8";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap5\BootstrapAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\Reclassering/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\4ceb4c69";s:7:"baseUrl";s:27:"/backoffice/assets/4ceb4c69";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:1:{s:8:"position";i:1;}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap5\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\Reclassering/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\4ceb4c69";s:7:"baseUrl";s:27:"/backoffice/assets/4ceb4c69";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:25:"kartik\grid\GridViewAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:4:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:22:"yii\grid\GridViewAsset";i:2;s:16:"yii\web\YiiAsset";i:3;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:15:"css/kv-grid.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:25:"kartik\dialog\DialogAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\Reclassering\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\1f8ca467";s:7:"baseUrl";s:27:"/backoffice/assets/1f8ca467";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:12:"js/dialog.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:1:{s:8:"position";i:1;}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:22:"yii\grid\GridViewAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:15:"yii.gridView.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"yii2ajaxcrud\ajaxcrud\FontAwesomeAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/fortawesome/font-awesome";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\713ebcc9";s:7:"baseUrl";s:27:"/backoffice/assets/713ebcc9";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:13:"js/all.min.js";}s:3:"css";a:1:{i:0;s:15:"css/all.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:31:"kartik\grid\CheckboxColumnAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:3:{i:0;s:25:"kartik\grid\GridViewAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/kv-grid-checkbox.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:32:"kartik\bs5dropdown\DropdownAsset";a:17:{s:10:"sourcePath";s:71:"C:\Web\Reclassering\vendor\kartik-v\yii2-bootstrap5-dropdown\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\14348fa8";s:7:"baseUrl";s:27:"/backoffice/assets/14348fa8";s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:35:"yii\bootstrap5\BootstrapPluginAsset";}s:2:"js";a:1:{i:0;s:14:"js/dropdown.js";}s:3:"css";a:1:{i:0;s:16:"css/dropdown.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:1;s:9:"bsVersion";s:3:"5.x";s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:34:"kartik\dialog\DialogBootstrapAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\Reclassering\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\1f8ca467";s:7:"baseUrl";s:27:"/backoffice/assets/1f8ca467";s:7:"depends";a:4:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";i:3;s:35:"yii\bootstrap5\BootstrapPluginAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap-dialog.js";}s:3:"css";a:1:{i:0;s:28:"css/bootstrap-dialog-bs4.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:1;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:28:"kartik\dialog\DialogYiiAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\Reclassering\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\1f8ca467";s:7:"baseUrl";s:27:"/backoffice/assets/1f8ca467";s:7:"depends";a:3:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:16:"js/dialog-yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:27:"kartik\grid\GridExportAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:3:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:20:"js/kv-grid-export.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:34:"kartik\grid\GridResizeColumnsAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\Reclassering\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a9ee7db7";s:7:"baseUrl";s:27:"/backoffice/assets/a9ee7db7";s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:25:"kartik\grid\GridViewAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:29:"js/jquery.resizableColumns.js";}s:3:"css";a:1:{i:0;s:31:"css/jquery.resizableColumns.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:21:"yii\widgets\PjaxAsset";a:9:{s:10:"sourcePath";s:48:"C:\Web\Reclassering/vendor/bower-asset/yii2-pjax";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\38002d64";s:7:"baseUrl";s:27:"/backoffice/assets/38002d64";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:14:"jquery.pjax.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:41:"hail812\adminlte3\assets\FontAwesomeAsset";a:9:{s:10:"sourcePath";s:74:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins/fontawesome-free";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\9a5ee6fe";s:7:"baseUrl";s:27:"/backoffice/assets/9a5ee6fe";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:15:"css/all.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"hail812\adminlte3\assets\AdminLteAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\f545690b";s:7:"baseUrl";s:27:"/backoffice/assets/f545690b";s:7:"depends";a:2:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";i:1;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:1:{i:0;s:18:"js/adminlte.min.js";}s:3:"css";a:1:{i:0;s:20:"css/adminlte.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:34:"hail812\adminlte3\assets\BaseAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";N;s:7:"baseUrl";N;s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";i:2;s:35:"yii\bootstrap4\BootstrapPluginAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap4\BootstrapAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap4\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:2:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:36:"hail812\adminlte3\assets\PluginAsset";a:9:{s:10:"sourcePath";s:57:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\834b4d89";s:7:"baseUrl";s:27:"/backoffice/assets/834b4d89";s:7:"depends";a:1:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:46:"/backoffice/assets/e4a6bb80/control_sidebar.js";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:31:"C:\Web\Reclassering\backend\web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:39:"\hail812\adminlte3\assets\AdminLteAsset";}s:2:"js";a:1:{i:0;a:1:{i:0;s:45:"backoffice/assets/e4a6bb80/control_sidebar.js";}}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:39:"\hail812\adminlte3\assets\AdminLteAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\f545690b";s:7:"baseUrl";s:27:"/backoffice/assets/f545690b";s:7:"depends";a:2:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";i:1;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:1:{i:0;s:18:"js/adminlte.min.js";}s:3:"css";a:1:{i:0;s:20:"css/adminlte.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:23:"backend\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:31:"C:\Web\Reclassering\backend\web";s:7:"baseUrl";s:11:"/backoffice";s:7:"depends";a:7:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:19:"yii\web\JqueryAsset";i:3;s:16:"yii\jui\JuiAsset";i:4;s:29:"kartik\sortable\SortableAsset";i:5;s:38:"hail812\adminlte3\assets\AdminLteAsset";i:6;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:3:{i:0;s:50:"https://unpkg.com/lucide@latest/dist/umd/lucide.js";i:1;s:10:"js/main.js";i:2;s:75:"https://cdn.jsdelivr.net/npm/sweetalert2@11.7.1/dist/sweetalert2.all.min.js";}s:3:"css";a:1:{i:0;s:12:"css/site.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\jui\JuiAsset";a:9:{s:10:"sourcePath";s:48:"C:\Web\Reclassering/vendor/bower-asset/jquery-ui";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a78fe255";s:7:"baseUrl";s:27:"/backoffice/assets/a78fe255";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:12:"jquery-ui.js";}s:3:"css";a:1:{i:0;s:31:"themes/smoothness/jquery-ui.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"kartik\sortable\SortableAsset";a:17:{s:10:"sourcePath";s:60:"C:\Web\Reclassering\vendor\kartik-v\yii2-sortable\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\3202e5a2";s:7:"baseUrl";s:27:"/backoffice/assets/3202e5a2";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:2:{i:0;s:19:"js/html5sortable.js";i:1;s:23:"js/kv-html5-sortable.js";}s:3:"css";a:1:{i:0;s:25:"css/kv-html5-sortable.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68d537563a8a3";s:3:"url";s:62:"http://localhost:8005/backoffice/index.php?r=notification-role";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:**********.161808;s:10:"statusCode";i:200;s:8:"sqlCount";i:35;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:12232728;s:14:"processingTime";d:0.3161919116973877;}s:10:"exceptions";a:0:{}}