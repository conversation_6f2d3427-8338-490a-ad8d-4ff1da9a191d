<?php

namespace common\components;

use Yii;
use yii\base\Component;
use common\models\Notification;
use common\models\NotificationTrigger;
use common\models\NotificationUser;
use yii\web\Request;

class NotificationManager extends Component
{
    /**
     * Check the request route against triggers and create notifications if matched.
     *
     * @param string $route   Current route (/controller/action)
     * @param Request $request
     */
    public function checkAndTrigger(string $route, Request $request): void
    {
        // Find trigger for this route
        $triggers = NotificationTrigger::find()->where(['route' => $route])->all();
        if (empty($triggers)) {
            return;
        }

        foreach ($triggers as $trigger) {
            // Check request type
            if ($trigger->request_type === 'AJAX' && !$request->isAjax) {
                continue;
            }
            if ($trigger->request_type === 'NON_AJAX' && $request->isAjax) {
                continue;
            }

            // Find the notification definition
            $notification = Notification::findOne(['key' => $trigger->notification_key, 'enabled' => 1]);
            if (!$notification) {
                continue;
            }

            // Prepare message (can later support template variables)
            $message = $notification->message_template;

            // Get users by roles linked to this notification
            $users = (new \yii\db\Query())
                ->select('u.id')
                ->from('user u')
                ->innerJoin('notification_role nr', 'nr.role_id = u.role_id')
                ->where(['nr.notification_id' => $notification->id])
                ->all();

            foreach ($users as $user) {
                $nu = new NotificationUser([
                    'notification_id' => $notification->id,
                    'user_id'         => $user['id'],
                    'message'         => $message,
                    'link'            => $trigger->link_template,
                ]);
                $nu->save(false);
            }

            // Optional: send email if flag enabled
            if ($notification->send_email) {
                foreach ($users as $user) {
                    $this->sendEmail($user['id'], $notification, $message, $trigger->link_template);
                }
            }
        }
    }

    /**
     * Example email sender
     */
    protected function sendEmail($userId, Notification $notification, string $message, ?string $link = null): void
    {
        $user = \common\models\User::findOne($userId);
        if (!$user || !$user->email) {
            return;
        }

        Yii::$app->mailer->compose()
            ->setTo($user->email)
            ->setSubject($notification->title)
            ->setTextBody($message . ($link ? "\n\n" . Yii::$app->urlManager->createAbsoluteUrl($link) : ''))
            ->send();
    }
}
