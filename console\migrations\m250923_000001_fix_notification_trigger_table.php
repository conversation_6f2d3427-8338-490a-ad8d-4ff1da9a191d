<?php

use yii\db\Migration;

/**
 * Fixes the notification_trigger table to match the current code expectations
 */
class m250923_000001_fix_notification_trigger_table extends Migration
{
    public function safeUp()
    {
        // Add the request_type column that the current code expects
        $this->addColumn('{{%notification_trigger}}', 'request_type', 
            $this->string()->defaultValue('ANY')->after('notification_key'));
        
        // Remove the unique constraint on route since we now need route + request_type to be unique
        $this->dropIndex('route', '{{%notification_trigger}}');
        
        // Add new unique constraint for route + notification_key + request_type
        $this->createIndex('idx_notification_trigger_unique', '{{%notification_trigger}}', 
            ['route', 'notification_key', 'request_type'], true);
        
        // Remove columns that are no longer used by the current code
        $this->dropColumn('{{%notification_trigger}}', 'model_class');
        $this->dropColumn('{{%notification_trigger}}', 'model_id_param');
        $this->dropColumn('{{%notification_trigger}}', 'fields');
        
        // Update existing data to have request_type = 'ANY' (already set as default)
        echo "Updated notification_trigger table structure.\n";
    }

    public function safeDown()
    {
        // Add back the removed columns
        $this->addColumn('{{%notification_trigger}}', 'model_class', $this->string()->notNull());
        $this->addColumn('{{%notification_trigger}}', 'model_id_param', $this->string()->notNull());
        $this->addColumn('{{%notification_trigger}}', 'fields', $this->json()->null());
        
        // Remove the new unique constraint
        $this->dropIndex('idx_notification_trigger_unique', '{{%notification_trigger}}');
        
        // Add back the original unique constraint on route
        $this->createIndex('route', '{{%notification_trigger}}', 'route', true);
        
        // Remove the request_type column
        $this->dropColumn('{{%notification_trigger}}', 'request_type');
        
        echo "Reverted notification_trigger table structure.\n";
    }
}
