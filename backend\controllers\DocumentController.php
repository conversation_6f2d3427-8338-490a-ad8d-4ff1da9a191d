<?php

namespace backend\controllers;

use common\components\SignatureHelper;
use common\components\WorkflowHelper;
use Yii;
use common\models\Document;
use common\models\DocumentAntwoord;
use common\models\DocumentSignature;
use common\models\search\DocumentSearch;
use DateTime;
use kartik\mpdf\Pdf;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use \yii\web\Response;
use yii\helpers\Html;
use yii\helpers\Url;

/**
 * DocumentController implements the CRUD actions for Document model.
 */
class DocumentController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['post'],
                    'bulkdelete' => ['post'],
                ],
            ],

            // Restrict actions by role
            'access' => [
                'class' => AccessControl::class,
                'only' => ['get-document'], // actions to protect
                'rules' => [
                    [
                        'allow' => true,
                        'actions' => ['get-document'],
                        'matchCallback' => function ($rule, $action) {
                            return !Yii::$app->user->isGuest
                                && Yii::$app->user->identity->role_id == 1;
                        },
                    ],
                ],
            ],
        ];
    }

    /**
     * Lists all Document models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new DocumentSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * Displays a single Document model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $request = Yii::$app->request;
        if ($request->isAjax) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return [
                'title' =>
                Html::beginTag('div', ['class' => 'row w-100']) .

                    "Document #" . $id .
                    "Status: " . $this->findModel($id)->workflowStatus->label .
                    Html::endTag('div'),
                'content' => $this->renderAjax('view', [
                    'model' => $this->findModel($id),
                ]),
                'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                    Html::a(Yii::t('yii2-ajaxcrud', 'Update'), ['update', 'id' => $id], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
            ];
        } else {
            return $this->render('view', [
                'model' => $this->findModel($id),
            ]);
        }
    }

    /**
     * Creates a new Document model.
     * For ajax request will return json object
     * and for non-ajax request if creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $request = Yii::$app->request;
        $model = new Document();

        // Check if we're dealing with a form submission
        if ($model->load($request->post())) {
            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Set timestamp and user fields
                $model->created_by = Yii::$app->user->id;
                $model->updated_by = Yii::$app->user->id;

                // Set filename using documentNaam
                // $model->filename = $model->filename . ' ' . date('d-m-Y');

                if (!$model->save()) {
                    throw new \Exception('Failed to save Document: ' . json_encode($model->errors));
                }

                // Save Answers
                $answers = $request->post('Answers', []);
                foreach ($answers as $vraagId => $antwoord) {
                    $documentAntwoord = new DocumentAntwoord();
                    $documentAntwoord->document_id = $model->id;
                    $documentAntwoord->vraag_id = $vraagId;
                    $documentAntwoord->antwoord = $antwoord;
                    $documentAntwoord->updated_by = Yii::$app->user->id;

                    if (!$documentAntwoord->save()) {
                        throw new \Exception('Failed to save answer for question ' . $vraagId . ': ' . json_encode($documentAntwoord->errors));
                    }
                }

                // Create notification using createAndAssignNotification
                // try {
                //     Notification::createAndAssignNotification(
                //         1,
                //         null,
                //         $model->documentNaam->naam . ' door ' . Yii::$app->user->identity->username,
                //         $model->id
                //     );
                // } catch (\Exception $e) {
                //     Yii::error('Failed to create notification: ' . $e->getMessage(), 'Document');
                //     // Optionally handle the notification creation failure
                //     // You might want to log it but not fail the whole transaction
                // }

                $transaction->commit();

                // Handle response based on request type
                if ($request->isAjax) {
                    Yii::$app->response->format = Response::FORMAT_JSON;
                    return [
                        'forceReload' => '#crud-datatable-pjax',
                        'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " Document",
                        // 'size' => Modal::SIZE_LARGE,
                        'content' => '<span class="text-success">' . Yii::t('yii2-ajaxcrud', 'Create') . ' Document ' . Yii::t('yii2-ajaxcrud', 'Success') . '</span>',
                        'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                            Html::a(Yii::t('yii2-ajaxcrud', 'Create More'), ['create'], ['class' => 'btn btn-primary', 'role' => 'modal-remote'])
                    ];
                } else {
                    return $this->redirect(['view', 'id' => $model->id]);
                }
            } catch (\Exception $e) {
                $transaction->rollBack();

                if ($request->isAjax) {
                    Yii::$app->response->format = Response::FORMAT_JSON;
                    return [
                        'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " Document",
                        'content' => $this->renderAjax('create', ['model' => $model]),
                        'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                            Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit']),
                        'error' => $e->getMessage()
                    ];
                } else {
                    Yii::$app->session->setFlash('error', $e->getMessage());
                    return $this->render('create', ['model' => $model]);
                }
            }
        }

        // Display the form for GET requests
        if ($request->isAjax) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return [
                'title' => Yii::t('yii2-ajaxcrud', 'Create New') . " Document",
                'content' => $this->renderAjax('create', ['model' => $model]),
                'footer' => Html::button(Yii::t('yii2-ajaxcrud', 'Close'), ['class' => 'btn btn-default pull-left', 'data-bs-dismiss' => 'modal']) .
                    Html::button(Yii::t('yii2-ajaxcrud', 'Save'), ['class' => 'btn btn-primary', 'type' => 'submit'])
            ];
        } else {
            return $this->render('create', ['model' => $model]);
        }
    }
    /**
     * Updates an existing Document model.
     * For ajax request will return json object
     * and for non-ajax request if update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $request = Yii::$app->request;
        if ($request->isPost) {
            $transaction = Yii::$app->db->beginTransaction();
            try {
                // Save Answers
                $answers = $request->post('Answers', []);
                foreach ($answers as $vraagId => $antwoord) {
                    $documentAntwoord = DocumentAntwoord::findOne([
                        'document_id' => $model->id,
                        'vraag_id' => $vraagId
                    ]);

                    if ($documentAntwoord) {
                        $documentAntwoord->antwoord = $antwoord;
                        if (!$documentAntwoord->save()) {
                            throw new \Exception('Failed to save answer for question ' . $vraagId);
                        }
                    }
                }


                // Create notification
                // try {
                //     goToMedewerkerStatus($model);
                //     Notification::createAndAssignNotification(
                //         2,
                //         null,
                //         $model->documentType->type . ' updated by ' . Yii::$app->user->identity->username,
                //         $model->id
                //     );
                // } catch (\Exception $e) {
                //     Yii::error('Failed to create notification: ' . $e->getMessage(), 'Document');
                // }

                $transaction->commit();

                // Check if it's an AJAX request
                if ($request->isAjax) {
                    Yii::$app->response->format = Response::FORMAT_JSON;
                    return [
                        'success' => true,
                        'message' => 'Document successfully updated'
                    ];
                }

                // For non-AJAX requests, set flash message and redirect
                Yii::$app->session->setFlash('success', 'Document successfully updated');
                return $this->redirect(['view', 'id' => $model->id]);
            } catch (\Exception $e) {
                $transaction->rollBack();

                if ($request->isAjax) {
                    Yii::$app->response->format = Response::FORMAT_JSON;
                    return [
                        'success' => false,
                        'message' => $e->getMessage()
                    ];
                }

                Yii::$app->session->setFlash('error', $e->getMessage());
            }
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }


    /**
     * Delete an existing Document model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $request = Yii::$app->request;
        $this->findModel($id)->delete();

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
        } else {
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }
    }

    /**
     * Delete multiple existing Document model.
     * For ajax request will return json object
     * and for non-ajax request if deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionBulkdelete()
    {
        $request = Yii::$app->request;
        $pks = explode(',', $request->post('pks')); // Array or selected records primary keys
        foreach ($pks as $pk) {
            $model = $this->findModel($pk);
            $model->delete();
        }

        if ($request->isAjax) {
            /*
            *   Process for ajax request
            */
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ['forceClose' => true, 'forceReload' => '#crud-datatable-pjax'];
        } else {
            /*
            *   Process for non-ajax request
            */
            return $this->redirect(['index']);
        }
    }

    /**
     * Finds the Document model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Document the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Document::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionViewFile($filename)
    {
        $fullPath = Yii::getAlias('@frontend/web/uploads/documents/') . $filename;

        if (!file_exists($fullPath)) {
            throw new \yii\web\NotFoundHttpException("The file does not exist.");
        }

        return Yii::$app->response->sendFile($fullPath, $filename, ['inline' => true]);
    }


    public function actionGetVragen($docId)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        return getVragenByDocument($docId);
    }

    /**
     * Displays comments page for a Document model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException
     */
    public function actionComments($id)
    {
        $model = $this->findModel($id);
        $feedbackModel = new \common\models\DocumentFeedback();
        $feedbackModel->document_id = $id;

        $documentData = getDocument($id);

        $documentDetails = $documentData['success'] ? $documentData['data'] : [];

        return $this->render('comments', [
            'model' => $model,
            'feedbackModel' => $feedbackModel,
            'documentData' => $documentDetails,
        ]);
    }

    public function actionPdf($id)
    {
        $model = $this->findModel($id);

        // Decrypt signatures before rendering (like in actionDocument)
        foreach ($model->documentSignatures as $signature) {
            $signature->signature_base64 = SignatureHelper::decodeAndDecrypt(
                $signature->signature_base64,
                $signature->user_id
            );
            $signature->signed_at = (new DateTime($signature->signed_at))->format('d-m-Y');
        }

        // Use the Twig template rendering (like in actionDocument)
        $html = $model->renderWithLayout($id, $model->documentSignatures);

        $pdf = new Pdf([
            'mode' => Pdf::MODE_UTF8,
            'format' => Pdf::FORMAT_A4,
            'orientation' => Pdf::ORIENT_PORTRAIT,
            'destination' => Pdf::DEST_BROWSER,  // Display in browser, don't save
            'content' => $html,  // Use Twig-rendered HTML instead of _pdf partial
            'options' => [
                'title' => $model->documentType->type,
                'subject' => 'document document',
                'keywords' => 'document, document, pdf'
            ],
            'methods' => [
                'SetHeader' => [$model->filename . '||{PAGENO}'],
                'SetFooter' => ['Paramaribo, ' . date('d-m-Y')],
            ]
        ]);

        return $pdf->render();
    }

    public function actionGoToNextStatus($id = null)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $request = Yii::$app->request;
        $id = $id ?? $request->post('id');

        if (!$id) {
            return [
                'success' => false,
                'message' => 'Missing Document ID'
            ];
        }

        $model = $this->findModel($id);

        try {
            WorkflowHelper::goToNextStatus($model);
            $model->save();
            return [
                'success' => true,
                'message' => 'Status updated successfully'
            ];
        } catch (\Exception $e) {
            Yii::error('Error updating status: ' . $e->getMessage(), 'Document');
            return [
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ];
        }
    }

    public function actionDocument($id)
    {
        $document = Document::find()
            ->with(['documentType', 'documentSignatures'])
            ->where(['id' => $id])
            ->one();

        if (!$document) {
            throw new NotFoundHttpException('Document not found.');
        }

        // Define public file path
        $publicPath = Yii::getAlias('@frontend/web/uploads/documents/');
        $filename = $document->filename . ' ' . $document->documentType->type . '.pdf';
        $fullPath = $publicPath . $filename;

        // Save relative path in DB
        $document->file_path = 'uploads/documents/' . $filename;
        $document->save(false);

        // Delete decrypted signatures (optional security step)
        DocumentSignature::deleteAll(['document_id' => $id]);

        // If PDF already exists, stream it
        if (file_exists($fullPath)) {
            return Yii::$app->response->sendFile($fullPath, $filename, [
                'inline' => true, // 👈 This tells the browser to render it instead of downloading
            ]);
        }

        // Create folder if needed
        if (!file_exists($publicPath)) {
            mkdir($publicPath, 0775, true);
        }

        // Decrypt signatures before rendering
        foreach ($document->documentSignatures as $signature) {
            $signature->signature_base64 = SignatureHelper::decodeAndDecrypt(
                $signature->signature_base64,
                $signature->user_id
            );
            $signature->signed_at = (new DateTime($signature->signed_at))->format('d-m-Y');
        }

        // Render HTML
        $html = $document->renderWithLayout($id, $document->documentSignatures);

        // Generate and save PDF to disk
        $pdf = new Pdf([
            'mode' => Pdf::MODE_UTF8,
            'format' => Pdf::FORMAT_A4,
            'orientation' => Pdf::ORIENT_PORTRAIT,
            'destination' => Pdf::DEST_FILE,
            'filename' => $fullPath,
            'content' => $html,
            'options' => ['title' => 'Document'],
            'methods' => [
                'SetHeader' => [$document->filename . '||{PAGENO}'],
                'SetFooter' => ['Paramaribo, ' . date('d-m-Y')],
            ]
        ]);
        $pdf->render();


        // Stream the generated file
        return Yii::$app->response->sendFile($fullPath, $filename, ['inline' => true]);
    }

    public function actionDossiers()
    {
        // Fetch and group files just like we discussed
        $systemDocs = \common\models\Document::find()->all();
        $userUploads = \common\models\DocumentUpload::find()->all();

        $grouped = [];

        foreach ($systemDocs as $doc) {
            $filename = $doc->filename . ' ' . $doc->documentType->type . '.pdf';
            $name = normalizePersonName($filename);
            $grouped[$name][] = [
                'label' => $filename,
                'url' => Url::to(['view-file', 'filename' => $filename]),
                'source' => 'system',
            ];
        }

        foreach ($userUploads as $upload) {
            $filename = basename($upload->file_path);
            $name = normalizePersonName($filename);
            $grouped[$name][] = [
                'label' => $filename,
                'url' => Url::to(['view-file', 'filename' => $filename]),
                'source' => 'upload',
            ];
        }

        ksort($grouped);

        return $this->render('dossiers', [
            'grouped' => $grouped,
        ]);
    }

    /**
     * Get gedetineerde data for AJAX requests
     */
    public function actionGetGedetineerde()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $id = Yii::$app->request->get('id');
        if (!$id) {
            return ['success' => false, 'message' => 'No ID provided'];
        }

        $gedetineerde = \common\models\Gedetineerde::findOne($id);
        if (!$gedetineerde) {
            return ['success' => false, 'message' => 'Gedetineerde not found'];
        }

        return [
            'success' => true,
            'gedetineerde' => [
                'id' => $gedetineerde->id,
                'naam' => $gedetineerde->naam,
                'voornaam' => $gedetineerde->voornaam,
                'idnr' => $gedetineerde->idnr
            ]
        ];
    }

    public function actionGetDocument($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        return getDocument($id);
    }

    public function actionMarkDocumentAsFinal($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $document = Document::findOne($id);
        $request = Yii::$app->request;


        if (!$document) {
            return [
                'success' => false,
                'message' => 'Document not found'
            ];
        }

        if ($request->isPost) {
            try {
                $document->mark_final =  1;
                WorkflowHelper::setStatusToFinal($document);

                if ($document->save(false)) {
                    return [
                        'success' => true,
                        'message' => 'Document successfully marked as final'
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'Failed to mark document as final'
                    ];
                }
            } catch (\Throwable $th) {
                return [
                    'success' => false,
                    'message' => 'Failed to save Document: ' . $th->getMessage()
                ];
            }
        }
    }
}
