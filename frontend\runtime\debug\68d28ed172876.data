a:14:{s:6:"config";s:15911:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:71:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:34:"miloschuman/yii2-highcharts-widget";a:3:{s:4:"name";s:34:"miloschuman/yii2-highcharts-widget";s:7:"version";s:8:"11.0.0.0";s:5:"alias";a:1:{s:23:"@miloschuman/highcharts";s:65:"C:\Web\Reclassering\vendor/miloschuman/yii2-highcharts-widget/src";}}}}";s:3:"log";s:23487:"a:1:{s:8:"messages";a:60:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.413663;i:4;a:0:{}i:5;i:2896688;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.41431;i:4;a:0:{}i:5;i:3001912;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.414316;i:4;a:0:{}i:5;i:3002208;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.414618;i:4;a:0:{}i:5;i:3032208;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.414913;i:4;a:0:{}i:5;i:3059680;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.416023;i:4;a:0:{}i:5;i:3214496;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.416033;i:4;a:0:{}i:5;i:3215136;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.424133;i:4;a:0:{}i:5;i:4212936;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.430315;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5537032;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.43034;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5539312;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.43521;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5597128;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.436651;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5617848;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=9) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.450563;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6087664;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.467375;i:4;a:0:{}i:5;i:6780016;}i:24;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering8301292eb993d3ff19f0b1cfe988b789' AND (`expire` = 0 OR `expire` >**********)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.471091;i:4;a:0:{}i:5;i:6968032;}i:27;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.473117;i:4;a:0:{}i:5;i:7034272;}i:28;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering00d716905c8ed414aa0103ba17815795' AND (`expire` = 0 OR `expire` >**********)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.473224;i:4;a:0:{}i:5;i:7042800;}i:37;a:6:{i:0;s:40:"Route requested: 'document-upload/index'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.474433;i:4;a:0:{}i:5;i:7075240;}i:38;a:6:{i:0;s:35:"Route to run: document-upload/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.476723;i:4;a:0:{}i:5;i:7198624;}i:39;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering527672bf2f25cd8a0df37a27bc734e23' AND (`expire` = 0 OR `expire` >**********)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.478559;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:74;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:34;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:44:"C:\Web\Reclassering\frontend\config\main.php";s:4:"line";i:88;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7224808;}i:42;a:6:{i:0;s:76:"Running action: frontend\controllers\DocumentUploadController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.481513;i:4;a:0:{}i:5;i:7323392;}i:43;a:6:{i:0;s:35:"SELECT * FROM `role` WHERE `id`=100";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.485954;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7421152;}i:46;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.487486;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7445232;}i:49;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.489534;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7456064;}i:52;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.490367;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7456152;}i:55;a:6:{i:0;s:38:"SELECT * FROM `instantie` WHERE `id`=3";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.49278;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7549088;}i:58;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `instantie`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.493521;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7555472;}i:61;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.495295;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7569088;}i:64;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instantie' AND `kcu`.`TABLE_NAME` = 'instantie'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.496034;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7567912;}i:67;a:6:{i:0;s:81:"Rendering view file: C:\Web\Reclassering\frontend\views\document-upload\index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.499717;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:7774872;}i:68;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering527672bf2f25cd8a0df37a27bc734e23' AND (`expire` = 0 OR `expire` >**********)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.510137;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:74;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:34;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\helpers\CanAccessHelper.php";s:4:"line";i:6;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:8493160;}i:71;a:6:{i:0;s:192:"SELECT COUNT(*) FROM `document_upload` INNER JOIN `document_permission` `dp` ON `document_upload`.`id` = `dp`.`document_id` WHERE (`user_id`=9) AND ((`dp`.`user_id`=9) AND (`dp`.`can_view`=1))";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513142;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\frontend\views\document-upload\index.php";s:4:"line";i:111;s:8:"function";s:9:"getModels";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:8602192;}i:74;a:6:{i:0;s:5572:"PDOException: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'user_id' in where clause is ambiguous in C:\Web\Reclassering\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Command.php(443): yii\db\Command->queryInternal()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Query.php(497): yii\db\Command->queryScalar()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\db\ActiveQuery.php(373): yii\db\Query->queryScalar()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Query.php(368): yii\db\ActiveQuery->queryScalar()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\data\ActiveDataProvider.php(168): yii\db\Query->count()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\data\BaseDataProvider.php(170): yii\data\ActiveDataProvider->prepareTotalCount()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\data\ActiveDataProvider.php(105): yii\data\BaseDataProvider->getTotalCount()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\data\BaseDataProvider.php(102): yii\data\ActiveDataProvider->prepareModels()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\data\BaseDataProvider.php(115): yii\data\BaseDataProvider->prepare()
#11 C:\Web\Reclassering\frontend\views\document-upload\index.php(111): yii\data\BaseDataProvider->getModels()
#12 C:\Web\Reclassering\vendor\yiisoft\yii2\base\View.php(348): require('...')
#13 C:\Web\Reclassering\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile()
#14 C:\Web\Reclassering\vendor\yiisoft\yii2\base\View.php(157): yii\base\View->renderFile()
#15 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(413): yii\base\View->render()
#16 C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php(60): yii\base\Controller->render()
#17 [internal function]: frontend\controllers\DocumentUploadController->actionIndex()
#18 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(60): call_user_func_array()
#19 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(184): yii\base\InlineAction->runWithParams()
#20 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(555): yii\base\Controller->runAction()
#21 C:\Web\Reclassering\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction()
#22 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest()
#23 C:\Web\Reclassering\frontend\web\index.php(18): yii\base\Application->run()
#24 {main}

Next yii\db\IntegrityException: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'user_id' in where clause is ambiguous
The SQL being executed was: SELECT COUNT(*) FROM `document_upload` INNER JOIN `document_permission` `dp` ON `document_upload`.`id` = `dp`.`document_id` WHERE (`user_id`=9) AND ((`dp`.`user_id`=9) AND (`dp`.`can_view`=1)) in C:\Web\Reclassering\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Command.php(443): yii\db\Command->queryInternal()
#3 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Query.php(497): yii\db\Command->queryScalar()
#4 C:\Web\Reclassering\vendor\yiisoft\yii2\db\ActiveQuery.php(373): yii\db\Query->queryScalar()
#5 C:\Web\Reclassering\vendor\yiisoft\yii2\db\Query.php(368): yii\db\ActiveQuery->queryScalar()
#6 C:\Web\Reclassering\vendor\yiisoft\yii2\data\ActiveDataProvider.php(168): yii\db\Query->count()
#7 C:\Web\Reclassering\vendor\yiisoft\yii2\data\BaseDataProvider.php(170): yii\data\ActiveDataProvider->prepareTotalCount()
#8 C:\Web\Reclassering\vendor\yiisoft\yii2\data\ActiveDataProvider.php(105): yii\data\BaseDataProvider->getTotalCount()
#9 C:\Web\Reclassering\vendor\yiisoft\yii2\data\BaseDataProvider.php(102): yii\data\ActiveDataProvider->prepareModels()
#10 C:\Web\Reclassering\vendor\yiisoft\yii2\data\BaseDataProvider.php(115): yii\data\BaseDataProvider->prepare()
#11 C:\Web\Reclassering\frontend\views\document-upload\index.php(111): yii\data\BaseDataProvider->getModels()
#12 C:\Web\Reclassering\vendor\yiisoft\yii2\base\View.php(348): require('...')
#13 C:\Web\Reclassering\vendor\yiisoft\yii2\base\View.php(258): yii\base\View->renderPhpFile()
#14 C:\Web\Reclassering\vendor\yiisoft\yii2\base\View.php(157): yii\base\View->renderFile()
#15 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(413): yii\base\View->render()
#16 C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php(60): yii\base\Controller->render()
#17 [internal function]: frontend\controllers\DocumentUploadController->actionIndex()
#18 C:\Web\Reclassering\vendor\yiisoft\yii2\base\InlineAction.php(60): call_user_func_array()
#19 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Controller.php(184): yii\base\InlineAction->runWithParams()
#20 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Module.php(555): yii\base\Controller->runAction()
#21 C:\Web\Reclassering\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction()
#22 C:\Web\Reclassering\vendor\yiisoft\yii2\base\Application.php(387): yii\web\Application->handleRequest()
#23 C:\Web\Reclassering\frontend\web\index.php(18): yii\base\Application->run()
#24 {main}
Additional Information:
Array
(
    [0] => 23000
    [1] => 1052
    [2] => Column 'user_id' in where clause is ambiguous
)
";i:1;i:1;i:2;s:25:"yii\db\IntegrityException";i:3;d:**********.514658;i:4;a:0:{}i:5;i:8327280;}i:75;a:6:{i:0;s:93:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/exception.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.514815;i:4;a:0:{}i:5;i:8320184;}i:76;a:6:{i:0;s:101:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/previousException.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.515679;i:4;a:0:{}i:5;i:8448048;}i:77;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.516794;i:4;a:0:{}i:5;i:8529848;}i:78;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.517986;i:4;a:0:{}i:5;i:8589064;}i:79;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.519307;i:4;a:0:{}i:5;i:8595280;}i:80;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.520758;i:4;a:0:{}i:5;i:8596376;}i:81;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.522358;i:4;a:0:{}i:5;i:8602864;}i:82;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.523783;i:4;a:0:{}i:5;i:8552256;}i:83;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.52532;i:4;a:0:{}i:5;i:8609792;}i:84;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.526596;i:4;a:0:{}i:5;i:8498624;}i:85;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.527886;i:4;a:0:{}i:5;i:8513216;}i:86;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.529045;i:4;a:0:{}i:5;i:8504912;}i:87;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.530301;i:4;a:0:{}i:5;i:8519504;}i:88;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.531502;i:4;a:0:{}i:5;i:8524696;}i:89;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.532706;i:4;a:0:{}i:5;i:8512512;}i:90;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.533708;i:4;a:0:{}i:5;i:8562040;}i:91;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.535095;i:4;a:0:{}i:5;i:8563136;}i:92;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.536354;i:4;a:0:{}i:5;i:8568328;}i:93;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.537786;i:4;a:0:{}i:5;i:8569888;}i:94;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.539126;i:4;a:0:{}i:5;i:8556632;}i:95;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.54;i:4;a:0:{}i:5;i:8514400;}i:96;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.541082;i:4;a:0:{}i:5;i:8524896;}i:97;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.542345;i:4;a:0:{}i:5;i:8582464;}i:98;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.543503;i:4;a:0:{}i:5;i:8601200;}i:99;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.544709;i:4;a:0:{}i:5;i:8548416;}i:100;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.546157;i:4;a:0:{}i:5;i:8599176;}i:101;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2/views/errorHandler/callStackItem.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.547308;i:4;a:0:{}i:5;i:8536440;}}}";s:9:"profiling";s:19118:"a:3:{s:6:"memory";i:9107304;s:4:"time";d:0.15229392051696777;s:8:"messages";a:36:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.430347;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5540120;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.432223;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5583424;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.432246;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5583208;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.434804;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5595840;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.435304;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5598040;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.436026;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5600616;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.436671;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5618888;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.438382;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5621416;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=9) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.450601;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6088048;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=9) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.451385;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6090264;}i:25;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering8301292eb993d3ff19f0b1cfe988b789' AND (`expire` = 0 OR `expire` >**********)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.471143;i:4;a:0:{}i:5;i:6968568;}i:26;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering8301292eb993d3ff19f0b1cfe988b789' AND (`expire` = 0 OR `expire` >**********)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.472037;i:4;a:0:{}i:5;i:6970504;}i:29;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering00d716905c8ed414aa0103ba17815795' AND (`expire` = 0 OR `expire` >**********)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.473241;i:4;a:0:{}i:5;i:7043336;}i:30;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering00d716905c8ed414aa0103ba17815795' AND (`expire` = 0 OR `expire` >**********)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.473927;i:4;a:0:{}i:5;i:7045784;}i:40;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering527672bf2f25cd8a0df37a27bc734e23' AND (`expire` = 0 OR `expire` >**********)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.478607;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:74;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:34;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:44:"C:\Web\Reclassering\frontend\config\main.php";s:4:"line";i:88;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7226368;}i:41;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering527672bf2f25cd8a0df37a27bc734e23' AND (`expire` = 0 OR `expire` >**********)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.479449;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:74;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:34;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:44:"C:\Web\Reclassering\frontend\config\main.php";s:4:"line";i:88;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7228272;}i:44;a:6:{i:0;s:35:"SELECT * FROM `role` WHERE `id`=100";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.486024;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7422280;}i:45;a:6:{i:0;s:35:"SELECT * FROM `role` WHERE `id`=100";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.486888;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7423936;}i:47;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.487506;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7446144;}i:48;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.489502;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7454776;}i:50;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.489547;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7456976;}i:51;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.490235;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7459136;}i:53;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.490384;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7457192;}i:54;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.492335;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7460264;}i:56;a:6:{i:0;s:38:"SELECT * FROM `instantie` WHERE `id`=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.492798;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7550216;}i:57;a:6:{i:0;s:38:"SELECT * FROM `instantie` WHERE `id`=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.493297;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7552440;}i:59;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.493569;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7556384;}i:60;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.495274;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7567800;}i:62;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.495304;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7570000;}i:63;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.495917;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7572296;}i:65;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instantie' AND `kcu`.`TABLE_NAME` = 'instantie'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.496046;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7570232;}i:66;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instantie' AND `kcu`.`TABLE_NAME` = 'instantie'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.497658;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7573304;}i:69;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering527672bf2f25cd8a0df37a27bc734e23' AND (`expire` = 0 OR `expire` >**********)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.510237;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:74;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:34;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\helpers\CanAccessHelper.php";s:4:"line";i:6;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:8495040;}i:70;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering527672bf2f25cd8a0df37a27bc734e23' AND (`expire` = 0 OR `expire` >**********)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.511199;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:74;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:34;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\helpers\CanAccessHelper.php";s:4:"line";i:6;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:8496944;}i:72;a:6:{i:0;s:192:"SELECT COUNT(*) FROM `document_upload` INNER JOIN `document_permission` `dp` ON `document_upload`.`id` = `dp`.`document_id` WHERE (`user_id`=9) AND ((`dp`.`user_id`=9) AND (`dp`.`can_view`=1))";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513216;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\frontend\views\document-upload\index.php";s:4:"line";i:111;s:8:"function";s:9:"getModels";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:8603488;}i:73;a:6:{i:0;s:192:"SELECT COUNT(*) FROM `document_upload` INNER JOIN `document_permission` `dp` ON `document_upload`.`id` = `dp`.`document_id` WHERE (`user_id`=9) AND ((`dp`.`user_id`=9) AND (`dp`.`can_view`=1))";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514606;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\frontend\views\document-upload\index.php";s:4:"line";i:111;s:8:"function";s:9:"getModels";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:8634448;}}}";s:2:"db";s:18348:"a:1:{s:8:"messages";a:34:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.432246;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5583208;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.434804;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5595840;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.435304;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5598040;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.436026;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5600616;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.436671;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5618888;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.438382;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5621416;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=9) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.450601;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6088048;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=9) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.451385;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6090264;}i:25;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering8301292eb993d3ff19f0b1cfe988b789' AND (`expire` = 0 OR `expire` >**********)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.471143;i:4;a:0:{}i:5;i:6968568;}i:26;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering8301292eb993d3ff19f0b1cfe988b789' AND (`expire` = 0 OR `expire` >**********)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.472037;i:4;a:0:{}i:5;i:6970504;}i:29;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering00d716905c8ed414aa0103ba17815795' AND (`expire` = 0 OR `expire` >**********)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.473241;i:4;a:0:{}i:5;i:7043336;}i:30;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering00d716905c8ed414aa0103ba17815795' AND (`expire` = 0 OR `expire` >**********)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.473927;i:4;a:0:{}i:5;i:7045784;}i:40;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering527672bf2f25cd8a0df37a27bc734e23' AND (`expire` = 0 OR `expire` >**********)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.478607;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:74;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:34;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:44:"C:\Web\Reclassering\frontend\config\main.php";s:4:"line";i:88;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7226368;}i:41;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering527672bf2f25cd8a0df37a27bc734e23' AND (`expire` = 0 OR `expire` >**********)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.479449;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:74;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:34;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:44:"C:\Web\Reclassering\frontend\config\main.php";s:4:"line";i:88;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:7228272;}i:44;a:6:{i:0;s:35:"SELECT * FROM `role` WHERE `id`=100";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.486024;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7422280;}i:45;a:6:{i:0;s:35:"SELECT * FROM `role` WHERE `id`=100";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.486888;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7423936;}i:47;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.487506;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7446144;}i:48;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.489502;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7454776;}i:50;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.489547;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7456976;}i:51;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.490235;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7459136;}i:53;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.490384;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7457192;}i:54;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.492335;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7460264;}i:56;a:6:{i:0;s:38:"SELECT * FROM `instantie` WHERE `id`=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.492798;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7550216;}i:57;a:6:{i:0;s:38:"SELECT * FROM `instantie` WHERE `id`=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.493297;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7552440;}i:59;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.493569;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7556384;}i:60;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.495274;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7567800;}i:62;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.495304;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7570000;}i:63;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.495917;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7572296;}i:65;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instantie' AND `kcu`.`TABLE_NAME` = 'instantie'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.496046;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7570232;}i:66;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instantie' AND `kcu`.`TABLE_NAME` = 'instantie'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.497658;i:4;a:1:{i:0;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:48;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7573304;}i:69;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering527672bf2f25cd8a0df37a27bc734e23' AND (`expire` = 0 OR `expire` >**********)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.510237;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:74;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:34;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\helpers\CanAccessHelper.php";s:4:"line";i:6;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:8495040;}i:70;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclassering527672bf2f25cd8a0df37a27bc734e23' AND (`expire` = 0 OR `expire` >**********)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.511199;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:74;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\AccessHelper.php";s:4:"line";i:34;s:8:"function";s:20:"getCachedPermissions";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\helpers\CanAccessHelper.php";s:4:"line";i:6;s:8:"function";s:9:"canAccess";s:5:"class";s:30:"common\components\AccessHelper";s:4:"type";s:2:"->";}}i:5;i:8496944;}i:72;a:6:{i:0;s:192:"SELECT COUNT(*) FROM `document_upload` INNER JOIN `document_permission` `dp` ON `document_upload`.`id` = `dp`.`document_id` WHERE (`user_id`=9) AND ((`dp`.`user_id`=9) AND (`dp`.`can_view`=1))";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513216;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\frontend\views\document-upload\index.php";s:4:"line";i:111;s:8:"function";s:9:"getModels";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:8603488;}i:73;a:6:{i:0;s:192:"SELECT COUNT(*) FROM `document_upload` INNER JOIN `document_permission` `dp` ON `document_upload`.`id` = `dp`.`document_id` WHERE (`user_id`=9) AND ((`dp`.`user_id`=9) AND (`dp`.`can_view`=1))";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514606;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\frontend\views\document-upload\index.php";s:4:"line";i:111;s:8:"function";s:9:"getModels";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:69:"C:\Web\Reclassering\frontend\controllers\DocumentUploadController.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:8634448;}}}";s:5:"event";s:13409:"a:78:{i:0;a:5:{s:4:"time";d:**********.428548;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:**********.432204;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:**********.451872;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:**********.45199;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:**********.461719;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:**********.473988;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:**********.480878;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:**********.481472;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"frontend\controllers\DocumentUploadController";}i:8;a:5:{s:4:"time";d:**********.482405;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:9;a:5:{s:4:"time";d:**********.483202;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:10;a:5:{s:4:"time";d:**********.487445;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:11;a:5:{s:4:"time";d:**********.492378;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\Role";}i:12;a:5:{s:4:"time";d:**********.492693;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:13;a:5:{s:4:"time";d:**********.493399;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"common\models\Instantie";}i:14;a:5:{s:4:"time";d:**********.497695;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"common\models\Instantie";}i:15;a:5:{s:4:"time";d:**********.499704;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:16;a:5:{s:4:"time";d:**********.511363;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\models\DocumentUpload";}i:17;a:5:{s:4:"time";d:**********.511838;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:18;a:5:{s:4:"time";d:**********.514807;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:19;a:5:{s:4:"time";d:**********.51536;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:20;a:5:{s:4:"time";d:**********.515674;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:21;a:5:{s:4:"time";d:**********.516233;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:22;a:5:{s:4:"time";d:**********.516784;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:23;a:5:{s:4:"time";d:**********.517334;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:24;a:5:{s:4:"time";d:**********.517977;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:25;a:5:{s:4:"time";d:**********.51864;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:26;a:5:{s:4:"time";d:**********.519297;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:27;a:5:{s:4:"time";d:**********.519969;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:28;a:5:{s:4:"time";d:**********.520744;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:29;a:5:{s:4:"time";d:**********.521525;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:30;a:5:{s:4:"time";d:**********.522344;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:31;a:5:{s:4:"time";d:**********.523091;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:32;a:5:{s:4:"time";d:**********.523769;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:33;a:5:{s:4:"time";d:**********.524513;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:34;a:5:{s:4:"time";d:**********.525306;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:35;a:5:{s:4:"time";d:**********.526082;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:36;a:5:{s:4:"time";d:**********.526585;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:37;a:5:{s:4:"time";d:**********.527348;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:38;a:5:{s:4:"time";d:**********.527875;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:39;a:5:{s:4:"time";d:**********.528582;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:40;a:5:{s:4:"time";d:**********.529034;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:41;a:5:{s:4:"time";d:**********.529775;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:42;a:5:{s:4:"time";d:**********.530289;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:43;a:5:{s:4:"time";d:**********.530997;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:44;a:5:{s:4:"time";d:**********.531486;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:45;a:5:{s:4:"time";d:**********.532234;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:46;a:5:{s:4:"time";d:**********.532697;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:47;a:5:{s:4:"time";d:**********.533264;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:48;a:5:{s:4:"time";d:**********.5337;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:49;a:5:{s:4:"time";d:**********.534343;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:50;a:5:{s:4:"time";d:**********.535085;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:51;a:5:{s:4:"time";d:**********.535773;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:52;a:5:{s:4:"time";d:**********.536339;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:53;a:5:{s:4:"time";d:**********.537201;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:54;a:5:{s:4:"time";d:**********.537772;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:55;a:5:{s:4:"time";d:**********.53857;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:56;a:5:{s:4:"time";d:**********.539113;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:57;a:5:{s:4:"time";d:**********.539883;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:58;a:5:{s:4:"time";d:**********.539991;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:59;a:5:{s:4:"time";d:**********.540632;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:60;a:5:{s:4:"time";d:**********.541071;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:61;a:5:{s:4:"time";d:**********.541729;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:62;a:5:{s:4:"time";d:**********.542334;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:63;a:5:{s:4:"time";d:**********.542985;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:64;a:5:{s:4:"time";d:**********.543495;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:65;a:5:{s:4:"time";d:**********.543964;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:66;a:5:{s:4:"time";d:**********.5447;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:67;a:5:{s:4:"time";d:**********.545446;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:68;a:5:{s:4:"time";d:**********.546141;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:69;a:5:{s:4:"time";d:**********.546936;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:70;a:5:{s:4:"time";d:**********.5473;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:71;a:5:{s:4:"time";d:**********.547741;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:72;a:5:{s:4:"time";d:**********.551685;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:73;a:5:{s:4:"time";d:**********.551704;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:74;a:5:{s:4:"time";d:**********.552392;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:75;a:5:{s:4:"time";d:**********.552407;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:76;a:5:{s:4:"time";d:**********.552985;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:77;a:5:{s:4:"time";d:**********.553566;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.407181;s:3:"end";d:**********.560234;s:6:"memory";i:9107304;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:1283:"a:3:{s:8:"messages";a:6:{i:31;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.474388;i:4;a:0:{}i:5;i:7072416;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.474399;i:4;a:0:{}i:5;i:7073008;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.474404;i:4;a:0:{}i:5;i:7074240;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.474414;i:4;a:0:{}i:5;i:7074832;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.47442;i:4;a:0:{}i:5;i:7075424;}i:36;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.474424;i:4;a:0:{}i:5;i:7075640;}}s:5:"route";s:21:"document-upload/index";s:6:"action";s:60:"frontend\controllers\DocumentUploadController::actionIndex()";}";s:7:"request";s:9701:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:500;s:14:"requestHeaders";a:20:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"x-original-url";s:22:"/document-upload/index";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36";s:7:"referer";s:44:"http://localhost:8005/document-upload/upload";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:468:"advanced-frontend-fmz=djq872gpboff940vij118t0re9; _identity-frontend=e7ca8ee6a8228771b23735c2872a6486c9dd77a0d8fa2e7e66f74f833f8e15f7a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B9%2C%22rP0D2nsCh442nWbTCQ0ORNXjzh3YQJCz%22%2C2592000%5D%22%3B%7D; _csrf-frontend=92301fd7354def6d727f9b33775d1faf6960f869eb8d59455b5f08698f176465a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22W-iz-wcM-Y_o9dgQ7P5Qvwy-FgfjvS4_%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";s:13:"cache-control";s:9:"max-age=0";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68d28ed172876";s:16:"X-Debug-Duration";s:3:"147";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=68d28ed172876";s:10:"Set-Cookie";s:313:"_identity-frontend=e7ca8ee6a8228771b23735c2872a6486c9dd77a0d8fa2e7e66f74f833f8e15f7a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B9%2C%22rP0D2nsCh442nWbTCQ0ORNXjzh3YQJCz%22%2C2592000%5D%22%3B%7D; expires=Thu, 23 Oct 2025 12:13:05 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:21:"document-upload/index";s:6:"action";s:60:"frontend\controllers\DocumentUploadController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:109:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-f05439ad-5834-45b2-b9a9-81f6cdf55da1";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:582:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\GitHub CLI\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:10:"/index.php";s:3:"URL";s:10:"/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:10:"/index.php";s:15:"SCRIPT_FILENAME";s:42:"C:\Web\Reclassering\frontend\web\index.php";s:11:"REQUEST_URI";s:22:"/document-upload/index";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"65180";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:0:"";s:15:"PATH_TRANSLATED";s:42:"C:\Web\Reclassering\frontend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:33:"C:\Web\Reclassering\frontend\web\";s:12:"APPL_MD_PATH";s:16:"/LM/W3SVC/2/ROOT";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:13:"UNENCODED_URL";s:22:"/document-upload/index";s:19:"IIS_WasUrlRewritten";s:1:"1";s:19:"HTTP_X_ORIGINAL_URL";s:22:"/document-upload/index";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36";s:12:"HTTP_REFERER";s:44:"http://localhost:8005/document-upload/upload";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:468:"advanced-frontend-fmz=djq872gpboff940vij118t0re9; _identity-frontend=e7ca8ee6a8228771b23735c2872a6486c9dd77a0d8fa2e7e66f74f833f8e15f7a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B9%2C%22rP0D2nsCh442nWbTCQ0ORNXjzh3YQJCz%22%2C2592000%5D%22%3B%7D; _csrf-frontend=92301fd7354def6d727f9b33775d1faf6960f869eb8d59455b5f08698f176465a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22W-iz-wcM-Y_o9dgQ7P5Qvwy-FgfjvS4_%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:19:"HTTP_CONTENT_LENGTH";s:1:"0";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.397927;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:3:{s:21:"advanced-frontend-fmz";s:26:"djq872gpboff940vij118t0re9";s:18:"_identity-frontend";s:158:"e7ca8ee6a8228771b23735c2872a6486c9dd77a0d8fa2e7e66f74f833f8e15f7a:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[9,"rP0D2nsCh442nWbTCQ0ORNXjzh3YQJCz",2592000]";}";s:14:"_csrf-frontend";s:140:"92301fd7354def6d727f9b33775d1faf6960f869eb8d59455b5f08698f176465a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"W-iz-wcM-Y_o9dgQ7P5Qvwy-FgfjvS4_";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:4:{s:7:"__flash";a:0:{}s:11:"__returnUrl";s:22:"http://localhost:8005/";s:4:"__id";i:9;s:9:"__authKey";s:32:"rP0D2nsCh442nWbTCQ0ORNXjzh3YQJCz";}}";s:4:"user";s:1470:"a:5:{s:2:"id";i:9;s:8:"identity";a:12:{s:2:"id";s:1:"9";s:8:"username";s:24:"'parketwacht-medewerker'";s:8:"auth_key";s:34:"'rP0D2nsCh442nWbTCQ0ORNXjzh3YQJCz'";s:13:"password_hash";s:62:"'$2y$13$D4Y44zD7Dyy5UJogRmNnQuDrBlnoQK0GpuuVS4Rb6ZXKJmDuqIy3q'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:28:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1758197935";s:10:"updated_at";s:10:"1758197935";s:18:"verification_token";s:4:"null";s:7:"role_id";s:3:"100";s:12:"access_token";s:4:"null";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:369:"a:1:{s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:50:"C:\Web\Reclassering/vendor/bower-asset/jquery/dist";s:8:"basePath";s:48:"C:\Web\Reclassering\frontend\web\assets\8bc86ca8";s:7:"baseUrl";s:16:"/assets/8bc86ca8";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68d28ed172876";s:3:"url";s:43:"http://localhost:8005/document-upload/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:**********.397927;s:10:"statusCode";i:500;s:8:"sqlCount";i:17;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9107304;s:14:"processingTime";d:0.15229392051696777;}s:10:"exceptions";a:0:{}}