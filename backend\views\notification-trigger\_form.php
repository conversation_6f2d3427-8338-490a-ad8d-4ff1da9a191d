<?php
use yii\helpers\Html;
use yii\bootstrap5\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\NotificationTrigger */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="notification-trigger-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'route')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'notification_key')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'request_type')->dropDownList([ 'ANY' => 'ANY', 'AJAX' => 'AJAX', 'NON_AJAX' => 'NON AJAX', ], ['prompt' => '']) ?>

    <?= $form->field($model, 'link_template')->textInput(['maxlength' => true]) ?>

  
	<?php if (!Yii::$app->request->isAjax){ ?>
	  	<div class="form-group">
	        <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
	    </div>
	<?php } ?>

    <?php ActiveForm::end(); ?>
    
</div>
