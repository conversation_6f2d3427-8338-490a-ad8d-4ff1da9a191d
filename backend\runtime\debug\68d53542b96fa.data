a:14:{s:6:"config";s:15911:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:71:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:34:"miloschuman/yii2-highcharts-widget";a:3:{s:4:"name";s:34:"miloschuman/yii2-highcharts-widget";s:7:"version";s:8:"11.0.0.0";s:5:"alias";a:1:{s:23:"@miloschuman/highcharts";s:65:"C:\Web\Reclassering\vendor/miloschuman/yii2-highcharts-widget/src";}}}}";s:3:"log";s:64011:"a:1:{s:8:"messages";a:106:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.69378;i:4;a:0:{}i:5;i:3020400;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.694515;i:4;a:0:{}i:5;i:3125592;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.694522;i:4;a:0:{}i:5;i:3125888;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.694834;i:4;a:0:{}i:5;i:3155248;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.695103;i:4;a:0:{}i:5;i:3183360;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.696674;i:4;a:0:{}i:5;i:3403928;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.696686;i:4;a:0:{}i:5;i:3404568;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.701385;i:4;a:0:{}i:5;i:4304408;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.707285;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5628504;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.707308;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5696320;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.731416;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5754136;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.732394;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5774856;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.740944;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6179136;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.757962;i:4;a:0:{}i:5;i:6816088;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.761463;i:4;a:0:{}i:5;i:6996184;}i:25;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.761477;i:4;a:0:{}i:5;i:6996824;}i:26;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.763277;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7066744;}i:29;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.765878;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7078376;}i:32;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.767112;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7078160;}i:35;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.776954;i:4;a:0:{}i:5;i:7258552;}i:37;a:6:{i:0;s:43:"Route requested: 'document-feedback/create'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.777036;i:4;a:0:{}i:5;i:7323040;}i:38;a:6:{i:0;s:38:"Route to run: document-feedback/create";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.779276;i:4;a:0:{}i:5;i:7385096;}i:39;a:6:{i:0;s:171:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document-feedback/create', '1', '::1', 1, 'POST', '2025-09-25 13:27:46')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.788495;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7656984;}i:42;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='/document-feedback/create'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.796527;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7720432;}i:45;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `notification_trigger`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.797564;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7727504;}i:48;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.799773;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7736784;}i:51;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_trigger' AND `kcu`.`TABLE_NAME` = 'notification_trigger'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.800714;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7739712;}i:54;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.803491;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7766656;}i:57;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.805919;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7777592;}i:60;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.807129;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7783080;}i:63;a:6:{i:0;s:87:"SELECT * FROM `notification` WHERE (`key`='document-feedback/create') AND (`enabled`=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.809305;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7786464;}i:66;a:6:{i:0;s:124:"SELECT `u`.`id` FROM `user` `u` INNER JOIN `notification_role` `nr` ON nr.role_id = u.role_id WHERE `nr`.`notification_id`=3";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.810214;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:51;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7793640;}i:69;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.811298;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7823376;}i:72;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.813444;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7834264;}i:75;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.814295;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7836040;}i:78;a:6:{i:0;s:113:"INSERT INTO `notification_user` (`notification_id`, `user_id`, `message`, `link`) VALUES (3, 7, 'Feedback', NULL)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.816204;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:65;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7840848;}i:81;a:6:{i:0;s:113:"INSERT INTO `notification_user` (`notification_id`, `user_id`, `message`, `link`) VALUES (3, 8, 'Feedback', NULL)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.818422;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:65;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7845688;}i:84;a:6:{i:0;s:78:"Running action: backend\controllers\DocumentFeedbackController::actionCreate()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.820633;i:4;a:0:{}i:5;i:7901928;}i:85;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.822054;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8084592;}i:88;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.823745;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8094272;}i:91;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.824827;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8094936;}i:94;a:6:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:**********.827705;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:93;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:8116360;}i:95;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.828767;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8489744;}i:98;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.83094;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8503376;}i:101;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.831671;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8502184;}i:104;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='23'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.833321;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8505904;}i:107;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.836765;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8747208;}i:110;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.838645;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8756552;}i:113;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.83929;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8760872;}i:116;a:6:{i:0;s:86:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='hoofd')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.840701;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8764728;}i:119;a:6:{i:0;s:95:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='hoofd')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.845746;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8836256;}i:122;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_metadata`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.846329;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8845176;}i:125;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_metadata`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.847707;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8855248;}i:128;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_metadata' AND `kcu`.`TABLE_NAME` = 'sw_metadata'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.84834;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8858848;}i:131;a:6:{i:0;s:38:"SELECT * FROM `document` WHERE `id`=23";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.850277;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:8945944;}i:134;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.855274;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9085800;}i:137;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.856217;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9100160;}i:140;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.858492;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9111856;}i:143;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.859567;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9113168;}i:146;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.862108;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9118608;}i:149;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.862764;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9128528;}i:152;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=23";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.863185;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9138752;}i:155;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.863531;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9156216;}i:158;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.86445;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9167928;}i:161;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.864962;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9169224;}i:164;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.866697;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9265568;}i:167;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.867503;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9280056;}i:170;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.868799;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9290608;}i:173;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.869769;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9292256;}i:176;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=23";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.872642;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9356448;}i:179;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.873989;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9379248;}i:182;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=23";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.874965;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9385944;}i:185;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.876337;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9400152;}i:188;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.877302;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9417504;}i:191;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.879087;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9429088;}i:194;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.879916;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9430496;}i:197;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.881841;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9500608;}i:200;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.884234;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9582440;}i:203;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.885227;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9594264;}i:206;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.886185;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9605872;}i:209;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.887084;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9617480;}i:212;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.887991;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9629088;}i:215;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.888858;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9640696;}i:218;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.890027;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9652304;}i:221;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.891004;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9663912;}i:224;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.891923;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9675520;}i:227;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.892777;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9687288;}i:230;a:6:{i:0;s:307:"SELECT `sw_transition`.* FROM `sw_transition` LEFT JOIN `sw_status` ON `sw_status`.id = `sw_transition`.end_status_id AND `sw_status`.workflow_id = 'document-workflow' WHERE (`sw_transition`.`workflow_id`='document-workflow') AND (`sw_transition`.`start_status_id`='hoofd') ORDER BY `sw_status`.`sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.895473;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9725480;}i:233;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `sw_transition`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.896663;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9733480;}i:236;a:6:{i:0;s:33:"SHOW CREATE TABLE `sw_transition`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.898325;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9742024;}i:239;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_transition' AND `kcu`.`TABLE_NAME` = 'sw_transition'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.89921;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9746776;}i:242;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.901253;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9751144;}i:245;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.902085;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9759904;}i:248;a:6:{i:0;s:86:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='final')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.90353;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9773376;}i:251;a:6:{i:0;s:95:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='final')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.90455;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9782120;}i:254;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=7) AS `result`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.906596;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9818672;}i:257;a:6:{i:0;s:85:"SELECT EXISTS(SELECT * FROM `document_type` WHERE `document_type`.`id`=8) AS `result`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.90773;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9827424;}i:260;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.908407;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9830712;}i:263;a:6:{i:0;s:312:"SELECT `sw_transition`.* FROM `sw_transition` LEFT JOIN `sw_status` ON `sw_status`.id = `sw_transition`.end_status_id AND `sw_status`.workflow_id = 'document-workflow' WHERE (`sw_transition`.`workflow_id`='document-workflow') AND (`sw_transition`.`start_status_id`='medewerker') ORDER BY `sw_status`.`sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.909238;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:601;s:8:"function";s:13:"getTransition";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9834936;}i:266;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='onderhoofd')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.910218;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9843104;}i:269;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='onderhoofd')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.910965;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9851864;}i:272;a:6:{i:0;s:107:"UPDATE `document` SET `status`='document-workflow/medewerker', `updated_at`=CURRENT_TIMESTAMP WHERE `id`=23";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.912814;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9849256;}i:275;a:6:{i:0;s:78:"SELECT EXISTS(SELECT * FROM `document` WHERE `document`.`id`='23') AS `result`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.913845;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:115;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9860200;}i:278;a:6:{i:0;s:180:"INSERT INTO `document_feedback` (`document_id`, `comment`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES (23, '3test3', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.914838;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:115;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9856880;}i:281;a:6:{i:0;s:18:"Commit transaction";i:1;i:8;i:2;s:26:"yii\db\Transaction::commit";i:3;d:**********.915486;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:119;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}}i:5;i:9855544;}i:282;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.23355317115784, `memory_max`=9896544 WHERE `id`=5326";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.920474;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:9849304;}}}";s:9:"profiling";s:121993:"a:3:{s:6:"memory";i:10069584;s:4:"time";d:0.24289417266845703;s:8:"messages";a:178:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.707315;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5697128;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.729809;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5740432;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.729839;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5740216;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.731356;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5752848;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.731434;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5755048;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.731852;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5757624;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.732411;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5775896;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.73392;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5778424;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.74102;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6179520;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.74181;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6181888;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.763371;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7067656;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.765819;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7077088;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.765895;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7079288;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.766713;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7081200;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.767157;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7079840;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.768835;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7081696;}i:40;a:6:{i:0;s:171:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document-feedback/create', '1', '::1', 1, 'POST', '2025-09-25 13:27:46')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.788599;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7658344;}i:41;a:6:{i:0;s:171:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document-feedback/create', '1', '::1', 1, 'POST', '2025-09-25 13:27:46')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.794967;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7660136;}i:43;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='/document-feedback/create'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.796558;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7721936;}i:44;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='/document-feedback/create'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.797465;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7724096;}i:46;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `notification_trigger`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.797586;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7728792;}i:47;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `notification_trigger`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.79972;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7735096;}i:49;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.799791;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7738072;}i:50;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.800582;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7740368;}i:52;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_trigger' AND `kcu`.`TABLE_NAME` = 'notification_trigger'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.800731;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7741128;}i:53;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_trigger' AND `kcu`.`TABLE_NAME` = 'notification_trigger'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.802916;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7744064;}i:55;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.803513;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7767944;}i:56;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.805871;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7775920;}i:58;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.805936;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7778880;}i:59;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.806747;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7781040;}i:61;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.807149;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7784496;}i:62;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.808992;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7786728;}i:64;a:6:{i:0;s:87:"SELECT * FROM `notification` WHERE (`key`='document-feedback/create') AND (`enabled`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.809348;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7787864;}i:65;a:6:{i:0;s:87:"SELECT * FROM `notification` WHERE (`key`='document-feedback/create') AND (`enabled`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.810001;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7791208;}i:67;a:6:{i:0;s:124:"SELECT `u`.`id` FROM `user` `u` INNER JOIN `notification_role` `nr` ON nr.role_id = u.role_id WHERE `nr`.`notification_id`=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.810228;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:51;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7794824;}i:68;a:6:{i:0;s:124:"SELECT `u`.`id` FROM `user` `u` INNER JOIN `notification_role` `nr` ON nr.role_id = u.role_id WHERE `nr`.`notification_id`=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.810949;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:51;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7797160;}i:70;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.811314;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7824664;}i:71;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.813385;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7832592;}i:73;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.813461;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7835552;}i:74;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.814182;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7837976;}i:76;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.814308;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7837456;}i:77;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.815835;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7840928;}i:79;a:6:{i:0;s:113:"INSERT INTO `notification_user` (`notification_id`, `user_id`, `message`, `link`) VALUES (3, 7, 'Feedback', NULL)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.816244;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:65;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7842040;}i:80;a:6:{i:0;s:113:"INSERT INTO `notification_user` (`notification_id`, `user_id`, `message`, `link`) VALUES (3, 7, 'Feedback', NULL)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.818325;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:65;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7843392;}i:82;a:6:{i:0;s:113:"INSERT INTO `notification_user` (`notification_id`, `user_id`, `message`, `link`) VALUES (3, 8, 'Feedback', NULL)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.818432;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:65;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7846880;}i:83;a:6:{i:0;s:113:"INSERT INTO `notification_user` (`notification_id`, `user_id`, `message`, `link`) VALUES (3, 8, 'Feedback', NULL)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.820549;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:65;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7848232;}i:86;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.822066;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8085504;}i:87;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.823647;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8092976;}i:89;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.82379;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8095184;}i:90;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.824654;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8097488;}i:92;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.82485;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8095976;}i:93;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.827015;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8099608;}i:96;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.828795;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8490656;}i:97;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.830888;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8502088;}i:99;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.830956;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8504288;}i:100;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.831504;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8507096;}i:102;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.831689;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8503224;}i:103;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.833245;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8507376;}i:105;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='23'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.833331;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8507032;}i:106;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='23'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.83384;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8509264;}i:108;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.83683;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8748872;}i:109;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.838618;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8754512;}i:111;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.838656;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8758216;}i:112;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.839227;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8760624;}i:114;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.839307;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8762664;}i:115;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.840622;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8765272;}i:117;a:6:{i:0;s:86:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='hoofd')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.840711;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8766504;}i:118;a:6:{i:0;s:86:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='hoofd')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.841093;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8768776;}i:120;a:6:{i:0;s:95:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='hoofd')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.845789;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8838032;}i:121;a:6:{i:0;s:95:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='hoofd')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.846271;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8840552;}i:123;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_metadata`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.846341;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8846840;}i:124;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_metadata`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.847658;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8852568;}i:126;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_metadata`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.847752;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8856912;}i:127;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_metadata`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.848256;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8859320;}i:129;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_metadata' AND `kcu`.`TABLE_NAME` = 'sw_metadata'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.848352;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8866272;}i:130;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_metadata' AND `kcu`.`TABLE_NAME` = 'sw_metadata'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.84962;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8868880;}i:132;a:6:{i:0;s:38:"SELECT * FROM `document` WHERE `id`=23";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.850289;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:8947448;}i:133;a:6:{i:0;s:38:"SELECT * FROM `document` WHERE `id`=23";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.8507;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:8949936;}i:135;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.85533;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9087304;}i:136;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.856083;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9091328;}i:138;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.856243;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9101448;}i:139;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.858426;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9110184;}i:141;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.858517;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9113144;}i:142;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.85939;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9115688;}i:144;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.859592;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9114584;}i:145;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.861688;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9118048;}i:147;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.862244;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9120112;}i:148;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.862632;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9122624;}i:150;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.862776;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9130032;}i:151;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.863079;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9132712;}i:153;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=23";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.863196;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9140256;}i:154;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=23";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.863469;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9147240;}i:156;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.863542;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9157504;}i:157;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.864418;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9166256;}i:159;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.864463;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9169216;}i:160;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.864871;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9171896;}i:162;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.864976;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9170640;}i:163;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.866003;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9174640;}i:165;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.866718;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9265816;}i:166;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.867239;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9272784;}i:168;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.867557;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9281344;}i:169;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.868751;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9288304;}i:171;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.868819;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9291896;}i:172;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.869636;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9294304;}i:174;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.869792;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9293672;}i:175;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.87205;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9297120;}i:177;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=23";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.872664;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9357952;}i:178;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=23";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.873363;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9360648;}i:180;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.874049;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9380752;}i:181;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.874695;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9383648;}i:183;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=23";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.874989;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9387448;}i:184;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=23";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.875679;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9389080;}i:186;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.876359;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9401656;}i:187;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.877043;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9408240;}i:189;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.87735;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9418792;}i:190;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.878997;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9427416;}i:192;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.879176;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9430376;}i:193;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.879786;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9432928;}i:195;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.879936;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9431912;}i:196;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.88137;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9435376;}i:198;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.881866;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9500856;}i:199;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.882571;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9507824;}i:201;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.884283;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9583624;}i:202;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.884948;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9586136;}i:204;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.88525;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9595448;}i:205;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.885946;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9597960;}i:207;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.886207;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9607056;}i:208;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.886835;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9609568;}i:210;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.887107;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9618664;}i:211;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.887741;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9621176;}i:213;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.888013;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9630272;}i:214;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.888632;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9632784;}i:216;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.888878;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9641880;}i:217;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.889494;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9644392;}i:219;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.890183;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9653488;}i:220;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.890746;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9656000;}i:222;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.891028;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9665096;}i:223;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.891699;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9667608;}i:225;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.891943;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9676704;}i:226;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.892562;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9679216;}i:228;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.892798;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9688472;}i:229;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.893372;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9690984;}i:231;a:6:{i:0;s:307:"SELECT `sw_transition`.* FROM `sw_transition` LEFT JOIN `sw_status` ON `sw_status`.id = `sw_transition`.end_status_id AND `sw_status`.workflow_id = 'document-workflow' WHERE (`sw_transition`.`workflow_id`='document-workflow') AND (`sw_transition`.`start_status_id`='hoofd') ORDER BY `sw_status`.`sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.895533;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9726840;}i:232;a:6:{i:0;s:307:"SELECT `sw_transition`.* FROM `sw_transition` LEFT JOIN `sw_status` ON `sw_status`.id = `sw_transition`.end_status_id AND `sw_status`.workflow_id = 'document-workflow' WHERE (`sw_transition`.`workflow_id`='document-workflow') AND (`sw_transition`.`start_status_id`='hoofd') ORDER BY `sw_status`.`sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.896466;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9730064;}i:234;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `sw_transition`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.89671;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9735144;}i:235;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `sw_transition`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.898264;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9739976;}i:237;a:6:{i:0;s:33:"SHOW CREATE TABLE `sw_transition`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.898348;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9743688;}i:238;a:6:{i:0;s:33:"SHOW CREATE TABLE `sw_transition`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.899107;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9746160;}i:240;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_transition' AND `kcu`.`TABLE_NAME` = 'sw_transition'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.899229;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9748568;}i:241;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_transition' AND `kcu`.`TABLE_NAME` = 'sw_transition'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.901036;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9751176;}i:243;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.901268;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9752920;}i:244;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.901913;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9755208;}i:246;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.902098;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9761680;}i:247;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.902762;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9764240;}i:249;a:6:{i:0;s:86:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='final')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.903545;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9775152;}i:250;a:6:{i:0;s:86:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='final')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.904013;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9777424;}i:252;a:6:{i:0;s:95:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='final')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.904595;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9783896;}i:253;a:6:{i:0;s:95:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='final')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.905251;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9786416;}i:255;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=7) AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.906625;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9819800;}i:256;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=7) AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.90728;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9820728;}i:258;a:6:{i:0;s:85:"SELECT EXISTS(SELECT * FROM `document_type` WHERE `document_type`.`id`=8) AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.90778;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9828552;}i:259;a:6:{i:0;s:85:"SELECT EXISTS(SELECT * FROM `document_type` WHERE `document_type`.`id`=8) AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.908273;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9829512;}i:261;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.908422;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9831840;}i:262;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.909036;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9832768;}i:264;a:6:{i:0;s:312:"SELECT `sw_transition`.* FROM `sw_transition` LEFT JOIN `sw_status` ON `sw_status`.id = `sw_transition`.end_status_id AND `sw_status`.workflow_id = 'document-workflow' WHERE (`sw_transition`.`workflow_id`='document-workflow') AND (`sw_transition`.`start_status_id`='medewerker') ORDER BY `sw_status`.`sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.909252;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:601;s:8:"function";s:13:"getTransition";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9836616;}i:265;a:6:{i:0;s:312:"SELECT `sw_transition`.* FROM `sw_transition` LEFT JOIN `sw_status` ON `sw_status`.id = `sw_transition`.end_status_id AND `sw_status`.workflow_id = 'document-workflow' WHERE (`sw_transition`.`workflow_id`='document-workflow') AND (`sw_transition`.`start_status_id`='medewerker') ORDER BY `sw_status`.`sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.910041;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:601;s:8:"function";s:13:"getTransition";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9839856;}i:267;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='onderhoofd')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.910233;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9844880;}i:268;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='onderhoofd')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.910813;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9847168;}i:270;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='onderhoofd')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.910979;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9853640;}i:271;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='onderhoofd')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.911561;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9856200;}i:273;a:6:{i:0;s:107:"UPDATE `document` SET `status`='document-workflow/medewerker', `updated_at`=CURRENT_TIMESTAMP WHERE `id`=23";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.912862;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9850280;}i:274;a:6:{i:0;s:107:"UPDATE `document` SET `status`='document-workflow/medewerker', `updated_at`=CURRENT_TIMESTAMP WHERE `id`=23";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.913536;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9851304;}i:276;a:6:{i:0;s:78:"SELECT EXISTS(SELECT * FROM `document` WHERE `document`.`id`='23') AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.913859;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:115;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9861328;}i:277;a:6:{i:0;s:78:"SELECT EXISTS(SELECT * FROM `document` WHERE `document`.`id`='23') AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.914348;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:115;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9862224;}i:279;a:6:{i:0;s:180:"INSERT INTO `document_feedback` (`document_id`, `comment`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES (23, '3test3', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.914881;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:115;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9857696;}i:280;a:6:{i:0;s:180:"INSERT INTO `document_feedback` (`document_id`, `comment`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES (23, '3test3', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.915439;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:115;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9858736;}i:283;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.23355317115784, `memory_max`=9896544 WHERE `id`=5326";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.920493;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:9850648;}i:284;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.23355317115784, `memory_max`=9896544 WHERE `id`=5326";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.922765;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:9852048;}}}";s:2:"db";s:121222:"a:1:{s:8:"messages";a:176:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.729839;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5740216;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.731356;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5752848;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.731434;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5755048;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.731852;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5757624;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.732411;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5775896;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.73392;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5778424;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.74102;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6179520;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.74181;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6181888;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.763371;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7067656;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.765819;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7077088;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.765895;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7079288;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.766713;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7081200;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.767157;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7079840;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.768835;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7081696;}i:40;a:6:{i:0;s:171:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document-feedback/create', '1', '::1', 1, 'POST', '2025-09-25 13:27:46')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.788599;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7658344;}i:41;a:6:{i:0;s:171:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document-feedback/create', '1', '::1', 1, 'POST', '2025-09-25 13:27:46')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.794967;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7660136;}i:43;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='/document-feedback/create'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.796558;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7721936;}i:44;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='/document-feedback/create'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.797465;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7724096;}i:46;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `notification_trigger`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.797586;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7728792;}i:47;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `notification_trigger`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.79972;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7735096;}i:49;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.799791;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7738072;}i:50;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.800582;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7740368;}i:52;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_trigger' AND `kcu`.`TABLE_NAME` = 'notification_trigger'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.800731;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7741128;}i:53;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_trigger' AND `kcu`.`TABLE_NAME` = 'notification_trigger'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.802916;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:16;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7744064;}i:55;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.803513;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7767944;}i:56;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.805871;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7775920;}i:58;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.805936;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7778880;}i:59;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.806747;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7781040;}i:61;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.807149;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7784496;}i:62;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.808992;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7786728;}i:64;a:6:{i:0;s:87:"SELECT * FROM `notification` WHERE (`key`='document-feedback/create') AND (`enabled`=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.809348;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7787864;}i:65;a:6:{i:0;s:87:"SELECT * FROM `notification` WHERE (`key`='document-feedback/create') AND (`enabled`=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.810001;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:31;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7791208;}i:67;a:6:{i:0;s:124:"SELECT `u`.`id` FROM `user` `u` INNER JOIN `notification_role` `nr` ON nr.role_id = u.role_id WHERE `nr`.`notification_id`=3";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.810228;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:51;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7794824;}i:68;a:6:{i:0;s:124:"SELECT `u`.`id` FROM `user` `u` INNER JOIN `notification_role` `nr` ON nr.role_id = u.role_id WHERE `nr`.`notification_id`=3";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.810949;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:51;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7797160;}i:70;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.811314;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7824664;}i:71;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.813385;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7832592;}i:73;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.813461;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7835552;}i:74;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.814182;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7837976;}i:76;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.814308;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7837456;}i:77;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.815835;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:58;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7840928;}i:79;a:6:{i:0;s:113:"INSERT INTO `notification_user` (`notification_id`, `user_id`, `message`, `link`) VALUES (3, 7, 'Feedback', NULL)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.816244;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:65;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7842040;}i:80;a:6:{i:0;s:113:"INSERT INTO `notification_user` (`notification_id`, `user_id`, `message`, `link`) VALUES (3, 7, 'Feedback', NULL)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.818325;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:65;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7843392;}i:82;a:6:{i:0;s:113:"INSERT INTO `notification_user` (`notification_id`, `user_id`, `message`, `link`) VALUES (3, 8, 'Feedback', NULL)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.818432;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:65;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7846880;}i:83;a:6:{i:0;s:113:"INSERT INTO `notification_user` (`notification_id`, `user_id`, `message`, `link`) VALUES (3, 8, 'Feedback', NULL)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.820549;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:65;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:7848232;}i:86;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.822066;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8085504;}i:87;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.823647;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8092976;}i:89;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.82379;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8095184;}i:90;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.824654;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8097488;}i:92;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.82485;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8095976;}i:93;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.827015;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:92;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8099608;}i:96;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.828795;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8490656;}i:97;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.830888;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8502088;}i:99;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.830956;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8504288;}i:100;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.831504;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8507096;}i:102;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.831689;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8503224;}i:103;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.833245;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8507376;}i:105;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='23'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.833331;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8507032;}i:106;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='23'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.83384;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8509264;}i:108;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.83683;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8748872;}i:109;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.838618;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8754512;}i:111;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.838656;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8758216;}i:112;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.839227;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8760624;}i:114;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.839307;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8762664;}i:115;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.840622;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8765272;}i:117;a:6:{i:0;s:86:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='hoofd')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.840711;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8766504;}i:118;a:6:{i:0;s:86:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='hoofd')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.841093;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8768776;}i:120;a:6:{i:0;s:95:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='hoofd')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.845789;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8838032;}i:121;a:6:{i:0;s:95:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='hoofd')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.846271;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8840552;}i:123;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_metadata`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.846341;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8846840;}i:124;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_metadata`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.847658;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8852568;}i:126;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_metadata`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.847752;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8856912;}i:127;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_metadata`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.848256;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8859320;}i:129;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_metadata' AND `kcu`.`TABLE_NAME` = 'sw_metadata'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.848352;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8866272;}i:130;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_metadata' AND `kcu`.`TABLE_NAME` = 'sw_metadata'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.84962;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:96;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8868880;}i:132;a:6:{i:0;s:38:"SELECT * FROM `document` WHERE `id`=23";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.850289;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:8947448;}i:133;a:6:{i:0;s:38:"SELECT * FROM `document` WHERE `id`=23";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.8507;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:8949936;}i:135;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.85533;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9087304;}i:136;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.856083;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9091328;}i:138;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.856243;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9101448;}i:139;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.858426;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9110184;}i:141;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.858517;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9113144;}i:142;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.85939;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9115688;}i:144;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.859592;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9114584;}i:145;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.861688;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9118048;}i:147;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.862244;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9120112;}i:148;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.862632;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9122624;}i:150;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.862776;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9130032;}i:151;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.863079;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9132712;}i:153;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=23";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.863196;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9140256;}i:154;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=23";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.863469;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9147240;}i:156;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.863542;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9157504;}i:157;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.864418;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9166256;}i:159;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.864463;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9169216;}i:160;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.864871;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9171896;}i:162;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.864976;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9170640;}i:163;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.866003;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9174640;}i:165;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.866718;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9265816;}i:166;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.867239;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9272784;}i:168;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.867557;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9281344;}i:169;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.868751;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9288304;}i:171;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.868819;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9291896;}i:172;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.869636;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9294304;}i:174;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.869792;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9293672;}i:175;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.87205;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9297120;}i:177;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=23";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.872664;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9357952;}i:178;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=23";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.873363;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9360648;}i:180;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.874049;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9380752;}i:181;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.874695;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9383648;}i:183;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=23";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.874989;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9387448;}i:184;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=23";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.875679;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9389080;}i:186;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.876359;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9401656;}i:187;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.877043;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9408240;}i:189;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.87735;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9418792;}i:190;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.878997;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9427416;}i:192;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.879176;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9430376;}i:193;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.879786;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9432928;}i:195;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.879936;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9431912;}i:196;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.88137;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9435376;}i:198;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.881866;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9500856;}i:199;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.882571;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9507824;}i:201;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.884283;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9583624;}i:202;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.884948;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9586136;}i:204;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.88525;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9595448;}i:205;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.885946;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9597960;}i:207;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.886207;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9607056;}i:208;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.886835;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9609568;}i:210;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.887107;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9618664;}i:211;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.887741;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9621176;}i:213;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.888013;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9630272;}i:214;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.888632;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9632784;}i:216;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.888878;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9641880;}i:217;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.889494;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9644392;}i:219;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.890183;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9653488;}i:220;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.890746;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9656000;}i:222;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.891028;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9665096;}i:223;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.891699;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9667608;}i:225;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.891943;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9676704;}i:226;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.892562;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9679216;}i:228;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.892798;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9688472;}i:229;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.893372;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:97;s:8:"function";s:11:"getDocument";}}i:5;i:9690984;}i:231;a:6:{i:0;s:307:"SELECT `sw_transition`.* FROM `sw_transition` LEFT JOIN `sw_status` ON `sw_status`.id = `sw_transition`.end_status_id AND `sw_status`.workflow_id = 'document-workflow' WHERE (`sw_transition`.`workflow_id`='document-workflow') AND (`sw_transition`.`start_status_id`='hoofd') ORDER BY `sw_status`.`sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.895533;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9726840;}i:232;a:6:{i:0;s:307:"SELECT `sw_transition`.* FROM `sw_transition` LEFT JOIN `sw_status` ON `sw_status`.id = `sw_transition`.end_status_id AND `sw_status`.workflow_id = 'document-workflow' WHERE (`sw_transition`.`workflow_id`='document-workflow') AND (`sw_transition`.`start_status_id`='hoofd') ORDER BY `sw_status`.`sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.896466;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9730064;}i:234;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `sw_transition`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.89671;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9735144;}i:235;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `sw_transition`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.898264;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9739976;}i:237;a:6:{i:0;s:33:"SHOW CREATE TABLE `sw_transition`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.898348;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9743688;}i:238;a:6:{i:0;s:33:"SHOW CREATE TABLE `sw_transition`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.899107;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9746160;}i:240;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_transition' AND `kcu`.`TABLE_NAME` = 'sw_transition'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.899229;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9748568;}i:241;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_transition' AND `kcu`.`TABLE_NAME` = 'sw_transition'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.901036;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:39;s:8:"function";s:20:"getStatusTransitions";s:5:"class";s:32:"common\components\WorkflowHelper";s:4:"type";s:2:"::";}}i:5;i:9751176;}i:243;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.901268;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9752920;}i:244;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.901913;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9755208;}i:246;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.902098;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9761680;}i:247;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.902762;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9764240;}i:249;a:6:{i:0;s:86:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='final')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.903545;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9775152;}i:250;a:6:{i:0;s:86:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='final')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.904013;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9777424;}i:252;a:6:{i:0;s:95:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='final')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.904595;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9783896;}i:253;a:6:{i:0;s:95:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='final')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.905251;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\common\helpers\WorkflowHelper.php";s:4:"line";i:24;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9786416;}i:255;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=7) AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.906625;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9819800;}i:256;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=7) AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.90728;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9820728;}i:258;a:6:{i:0;s:85:"SELECT EXISTS(SELECT * FROM `document_type` WHERE `document_type`.`id`=8) AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.90778;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9828552;}i:259;a:6:{i:0;s:85:"SELECT EXISTS(SELECT * FROM `document_type` WHERE `document_type`.`id`=8) AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.908273;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9829512;}i:261;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.908422;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9831840;}i:262;a:6:{i:0;s:67:"SELECT EXISTS(SELECT * FROM `user` WHERE `user`.`id`=1) AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.909036;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9832768;}i:264;a:6:{i:0;s:312:"SELECT `sw_transition`.* FROM `sw_transition` LEFT JOIN `sw_status` ON `sw_status`.id = `sw_transition`.end_status_id AND `sw_status`.workflow_id = 'document-workflow' WHERE (`sw_transition`.`workflow_id`='document-workflow') AND (`sw_transition`.`start_status_id`='medewerker') ORDER BY `sw_status`.`sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.909252;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:601;s:8:"function";s:13:"getTransition";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9836616;}i:265;a:6:{i:0;s:312:"SELECT `sw_transition`.* FROM `sw_transition` LEFT JOIN `sw_status` ON `sw_status`.id = `sw_transition`.end_status_id AND `sw_status`.workflow_id = 'document-workflow' WHERE (`sw_transition`.`workflow_id`='document-workflow') AND (`sw_transition`.`start_status_id`='medewerker') ORDER BY `sw_status`.`sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.910041;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:239;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:601;s:8:"function";s:13:"getTransition";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9839856;}i:267;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='onderhoofd')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.910233;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9844880;}i:268;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='onderhoofd')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.910813;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9847168;}i:270;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='onderhoofd')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.910979;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9853640;}i:271;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='onderhoofd')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.911561;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:245;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:262;s:8:"function";s:14:"getTransitions";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}}i:5;i:9856200;}i:273;a:6:{i:0;s:107:"UPDATE `document` SET `status`='document-workflow/medewerker', `updated_at`=CURRENT_TIMESTAMP WHERE `id`=23";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.912862;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9850280;}i:274;a:6:{i:0;s:107:"UPDATE `document` SET `status`='document-workflow/medewerker', `updated_at`=CURRENT_TIMESTAMP WHERE `id`=23";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.913536;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:111;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9851304;}i:276;a:6:{i:0;s:78:"SELECT EXISTS(SELECT * FROM `document` WHERE `document`.`id`='23') AS `result`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.913859;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:115;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9861328;}i:277;a:6:{i:0;s:78:"SELECT EXISTS(SELECT * FROM `document` WHERE `document`.`id`='23') AS `result`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.914348;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:115;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9862224;}i:279;a:6:{i:0;s:180:"INSERT INTO `document_feedback` (`document_id`, `comment`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES (23, '3test3', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.914881;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:115;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9857696;}i:280;a:6:{i:0;s:180:"INSERT INTO `document_feedback` (`document_id`, `comment`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES (23, '3test3', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.915439;i:4;a:1:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\backend\controllers\DocumentFeedbackController.php";s:4:"line";i:115;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9858736;}i:283;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.23355317115784, `memory_max`=9896544 WHERE `id`=5326";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.920493;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:9850648;}i:284;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.23355317115784, `memory_max`=9896544 WHERE `id`=5326";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.922765;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:9852048;}}}";s:5:"event";s:41909:"a:233:{i:0;a:5:{s:4:"time";d:**********.705573;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:**********.729798;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:**********.742335;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:**********.742384;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:**********.751291;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:**********.777005;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:**********.782194;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:7;a:5:{s:4:"time";d:**********.782261;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:8;a:5:{s:4:"time";d:**********.786956;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:9;a:5:{s:4:"time";d:**********.795304;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:10;a:5:{s:4:"time";d:**********.795324;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:**********.795602;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:46:"backend\controllers\DocumentFeedbackController";}i:12;a:5:{s:4:"time";d:**********.796399;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:13;a:5:{s:4:"time";d:**********.79751;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\models\NotificationTrigger";}i:14;a:5:{s:4:"time";d:**********.802986;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\models\NotificationTrigger";}i:15;a:5:{s:4:"time";d:**********.803429;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:16;a:5:{s:4:"time";d:**********.81003;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Notification";}i:17;a:5:{s:4:"time";d:**********.810056;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\Notification";}i:18;a:5:{s:4:"time";d:**********.815958;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:19;a:5:{s:4:"time";d:**********.816003;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:20;a:5:{s:4:"time";d:**********.818363;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:21;a:5:{s:4:"time";d:**********.818377;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:22;a:5:{s:4:"time";d:**********.818381;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:23;a:5:{s:4:"time";d:**********.820581;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:24;a:5:{s:4:"time";d:**********.821083;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:25;a:5:{s:4:"time";d:**********.827733;s:4:"name";s:16:"beginTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:26;a:5:{s:4:"time";d:**********.828718;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:27;a:5:{s:4:"time";d:**********.835442;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:28;a:5:{s:4:"time";d:**********.836619;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:29;a:5:{s:4:"time";d:**********.841159;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:30;a:5:{s:4:"time";d:**********.841248;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:31;a:5:{s:4:"time";d:**********.842291;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:32;a:5:{s:4:"time";d:**********.846297;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:43:"cornernote\workflow\manager\models\Metadata";}i:33;a:5:{s:4:"time";d:**********.849659;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:43:"cornernote\workflow\manager\models\Metadata";}i:34;a:5:{s:4:"time";d:**********.85021;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:35;a:5:{s:4:"time";d:**********.850228;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:36;a:5:{s:4:"time";d:**********.850902;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:37;a:5:{s:4:"time";d:**********.852084;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:38;a:5:{s:4:"time";d:**********.85305;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:39;a:5:{s:4:"time";d:**********.853107;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:40;a:5:{s:4:"time";d:**********.853144;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:41;a:5:{s:4:"time";d:**********.854082;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:42;a:5:{s:4:"time";d:**********.85414;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:43;a:5:{s:4:"time";d:**********.855028;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:44;a:5:{s:4:"time";d:**********.856172;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:45;a:5:{s:4:"time";d:**********.861851;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:46;a:5:{s:4:"time";d:**********.862673;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:47;a:5:{s:4:"time";d:**********.862701;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:48;a:5:{s:4:"time";d:**********.863111;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:49;a:5:{s:4:"time";d:**********.863133;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:50;a:5:{s:4:"time";d:**********.86351;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:51;a:5:{s:4:"time";d:**********.866075;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:52;a:5:{s:4:"time";d:**********.866103;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:53;a:5:{s:4:"time";d:**********.866123;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:54;a:5:{s:4:"time";d:**********.866141;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:55;a:5:{s:4:"time";d:**********.866159;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:56;a:5:{s:4:"time";d:**********.866176;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:57;a:5:{s:4:"time";d:**********.866193;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:58;a:5:{s:4:"time";d:**********.866209;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:59;a:5:{s:4:"time";d:**********.866226;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:60;a:5:{s:4:"time";d:**********.866594;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:61;a:5:{s:4:"time";d:**********.867416;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:62;a:5:{s:4:"time";d:**********.872175;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:63;a:5:{s:4:"time";d:**********.872215;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:64;a:5:{s:4:"time";d:**********.872244;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:65;a:5:{s:4:"time";d:**********.872268;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:66;a:5:{s:4:"time";d:**********.872292;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:67;a:5:{s:4:"time";d:**********.872316;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:68;a:5:{s:4:"time";d:**********.872339;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:69;a:5:{s:4:"time";d:**********.872361;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:70;a:5:{s:4:"time";d:**********.872383;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:71;a:5:{s:4:"time";d:**********.872397;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:72;a:5:{s:4:"time";d:**********.872402;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:73;a:5:{s:4:"time";d:**********.872407;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:74;a:5:{s:4:"time";d:**********.872412;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:75;a:5:{s:4:"time";d:**********.872416;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:76;a:5:{s:4:"time";d:**********.872421;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:77;a:5:{s:4:"time";d:**********.872425;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:78;a:5:{s:4:"time";d:**********.87243;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:79;a:5:{s:4:"time";d:**********.872434;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:80;a:5:{s:4:"time";d:**********.872439;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:81;a:5:{s:4:"time";d:**********.872494;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:82;a:5:{s:4:"time";d:**********.8725;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:83;a:5:{s:4:"time";d:**********.872504;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:84;a:5:{s:4:"time";d:**********.872509;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:85;a:5:{s:4:"time";d:**********.872514;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:86;a:5:{s:4:"time";d:**********.872518;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:87;a:5:{s:4:"time";d:**********.872523;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:88;a:5:{s:4:"time";d:**********.872528;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:89;a:5:{s:4:"time";d:**********.872532;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:90;a:5:{s:4:"time";d:**********.872537;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:91;a:5:{s:4:"time";d:**********.873543;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:92;a:5:{s:4:"time";d:**********.873688;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:93;a:5:{s:4:"time";d:**********.873773;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:94;a:5:{s:4:"time";d:**********.874768;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:95;a:5:{s:4:"time";d:**********.874818;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:96;a:5:{s:4:"time";d:**********.874849;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:97;a:5:{s:4:"time";d:**********.874855;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:98;a:5:{s:4:"time";d:**********.875763;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:99;a:5:{s:4:"time";d:**********.876245;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:100;a:5:{s:4:"time";d:**********.877211;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:101;a:5:{s:4:"time";d:**********.881476;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:102;a:5:{s:4:"time";d:**********.881518;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:103;a:5:{s:4:"time";d:**********.881547;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:104;a:5:{s:4:"time";d:**********.881573;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:105;a:5:{s:4:"time";d:**********.881598;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:106;a:5:{s:4:"time";d:**********.88162;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:107;a:5:{s:4:"time";d:**********.881643;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:108;a:5:{s:4:"time";d:**********.881665;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:109;a:5:{s:4:"time";d:**********.881689;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:110;a:5:{s:4:"time";d:**********.88172;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:111;a:5:{s:4:"time";d:**********.882737;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:112;a:5:{s:4:"time";d:**********.882859;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:113;a:5:{s:4:"time";d:**********.882946;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:114;a:5:{s:4:"time";d:**********.883027;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:115;a:5:{s:4:"time";d:**********.883106;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:116;a:5:{s:4:"time";d:**********.883184;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:117;a:5:{s:4:"time";d:**********.883262;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:118;a:5:{s:4:"time";d:**********.883338;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:119;a:5:{s:4:"time";d:**********.883416;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:120;a:5:{s:4:"time";d:**********.883523;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:121;a:5:{s:4:"time";d:**********.883567;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:122;a:5:{s:4:"time";d:**********.883583;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:123;a:5:{s:4:"time";d:**********.883597;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:124;a:5:{s:4:"time";d:**********.883611;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:125;a:5:{s:4:"time";d:**********.883625;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:126;a:5:{s:4:"time";d:**********.883639;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:127;a:5:{s:4:"time";d:**********.883652;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:128;a:5:{s:4:"time";d:**********.883666;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:129;a:5:{s:4:"time";d:**********.883682;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:130;a:5:{s:4:"time";d:**********.883696;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:131;a:5:{s:4:"time";d:**********.883815;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:132;a:5:{s:4:"time";d:**********.88383;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:133;a:5:{s:4:"time";d:**********.883844;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:134;a:5:{s:4:"time";d:**********.883858;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:135;a:5:{s:4:"time";d:**********.883871;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:136;a:5:{s:4:"time";d:**********.883885;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:137;a:5:{s:4:"time";d:**********.883898;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:138;a:5:{s:4:"time";d:**********.883912;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:139;a:5:{s:4:"time";d:**********.883925;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:140;a:5:{s:4:"time";d:**********.883938;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:141;a:5:{s:4:"time";d:**********.884044;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:142;a:5:{s:4:"time";d:**********.885019;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:143;a:5:{s:4:"time";d:**********.885069;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:144;a:5:{s:4:"time";d:**********.885116;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:145;a:5:{s:4:"time";d:**********.886008;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:146;a:5:{s:4:"time";d:**********.886051;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:147;a:5:{s:4:"time";d:**********.886092;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:148;a:5:{s:4:"time";d:**********.886904;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:149;a:5:{s:4:"time";d:**********.886949;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:150;a:5:{s:4:"time";d:**********.88699;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:151;a:5:{s:4:"time";d:**********.887799;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:152;a:5:{s:4:"time";d:**********.887843;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:153;a:5:{s:4:"time";d:**********.887884;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:154;a:5:{s:4:"time";d:**********.888687;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:155;a:5:{s:4:"time";d:**********.888729;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:156;a:5:{s:4:"time";d:**********.888768;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:157;a:5:{s:4:"time";d:**********.889623;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:158;a:5:{s:4:"time";d:**********.889724;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:159;a:5:{s:4:"time";d:**********.889814;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:160;a:5:{s:4:"time";d:**********.890806;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:161;a:5:{s:4:"time";d:**********.89085;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:162;a:5:{s:4:"time";d:**********.890892;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:163;a:5:{s:4:"time";d:**********.891751;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:164;a:5:{s:4:"time";d:**********.89179;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:165;a:5:{s:4:"time";d:**********.891827;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:166;a:5:{s:4:"time";d:**********.892615;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:167;a:5:{s:4:"time";d:**********.892654;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:168;a:5:{s:4:"time";d:**********.892691;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:169;a:5:{s:4:"time";d:**********.893498;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:170;a:5:{s:4:"time";d:**********.893589;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:171;a:5:{s:4:"time";d:**********.895181;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:172;a:5:{s:4:"time";d:**********.896554;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"cornernote\workflow\manager\models\Transition";}i:173;a:5:{s:4:"time";d:**********.901086;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"cornernote\workflow\manager\models\Transition";}i:174;a:5:{s:4:"time";d:**********.90112;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"cornernote\workflow\manager\models\Transition";}i:175;a:5:{s:4:"time";d:**********.901124;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"cornernote\workflow\manager\models\Transition";}i:176;a:5:{s:4:"time";d:**********.901154;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:177;a:5:{s:4:"time";d:**********.901941;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:178;a:5:{s:4:"time";d:**********.901966;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:179;a:5:{s:4:"time";d:**********.901991;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:180;a:5:{s:4:"time";d:**********.902789;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:43:"cornernote\workflow\manager\models\Metadata";}i:181;a:5:{s:4:"time";d:**********.902815;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:43:"cornernote\workflow\manager\models\Metadata";}i:182;a:5:{s:4:"time";d:**********.903445;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:183;a:5:{s:4:"time";d:**********.904105;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:184;a:5:{s:4:"time";d:**********.904195;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:185;a:5:{s:4:"time";d:**********.904273;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:186;a:5:{s:4:"time";d:**********.905297;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:43:"cornernote\workflow\manager\models\Metadata";}i:187;a:5:{s:4:"time";d:**********.905346;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:43:"cornernote\workflow\manager\models\Metadata";}i:188;a:5:{s:4:"time";d:**********.906073;s:4:"name";s:26:"EVENT_BEFORE_CHANGE_STATUS";s:5:"class";s:39:"raoul2000\workflow\events\WorkflowEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:46:"raoul2000\workflow\base\SimpleWorkflowBehavior";}i:189;a:5:{s:4:"time";d:**********.906085;s:4:"name";s:42:"beforeLeaveStatus{document-workflow/hoofd}";s:5:"class";s:39:"raoul2000\workflow\events\WorkflowEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:46:"raoul2000\workflow\base\SimpleWorkflowBehavior";}i:190;a:5:{s:4:"time";d:**********.906091;s:4:"name";s:79:"beforeChangeStatusFrom{document-workflow/hoofd}to{document-workflow/medewerker}";s:5:"class";s:39:"raoul2000\workflow\events\WorkflowEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:46:"raoul2000\workflow\base\SimpleWorkflowBehavior";}i:191;a:5:{s:4:"time";d:**********.906096;s:4:"name";s:47:"beforeEnterStatus{document-workflow/medewerker}";s:5:"class";s:39:"raoul2000\workflow\events\WorkflowEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:46:"raoul2000\workflow\base\SimpleWorkflowBehavior";}i:192;a:5:{s:4:"time";d:**********.90611;s:4:"name";s:25:"EVENT_AFTER_CHANGE_STATUS";s:5:"class";s:39:"raoul2000\workflow\events\WorkflowEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:46:"raoul2000\workflow\base\SimpleWorkflowBehavior";}i:193;a:5:{s:4:"time";d:**********.906115;s:4:"name";s:41:"afterLeaveStatus{document-workflow/hoofd}";s:5:"class";s:39:"raoul2000\workflow\events\WorkflowEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:46:"raoul2000\workflow\base\SimpleWorkflowBehavior";}i:194;a:5:{s:4:"time";d:**********.906121;s:4:"name";s:78:"afterChangeStatusFrom{document-workflow/hoofd}to{document-workflow/medewerker}";s:5:"class";s:39:"raoul2000\workflow\events\WorkflowEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:46:"raoul2000\workflow\base\SimpleWorkflowBehavior";}i:195;a:5:{s:4:"time";d:**********.906126;s:4:"name";s:46:"afterEnterStatus{document-workflow/medewerker}";s:5:"class";s:39:"raoul2000\workflow\events\WorkflowEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:46:"raoul2000\workflow\base\SimpleWorkflowBehavior";}i:196;a:5:{s:4:"time";d:**********.906142;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:197;a:5:{s:4:"time";d:**********.90645;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:198;a:5:{s:4:"time";d:**********.906487;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:199;a:5:{s:4:"time";d:**********.907439;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:200;a:5:{s:4:"time";d:**********.907522;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:201;a:5:{s:4:"time";d:**********.908319;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:202;a:5:{s:4:"time";d:**********.908343;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:203;a:5:{s:4:"time";d:**********.909065;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:204;a:5:{s:4:"time";d:**********.909161;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:205;a:5:{s:4:"time";d:**********.910067;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"cornernote\workflow\manager\models\Transition";}i:206;a:5:{s:4:"time";d:**********.910092;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"cornernote\workflow\manager\models\Transition";}i:207;a:5:{s:4:"time";d:**********.910114;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"cornernote\workflow\manager\models\Transition";}i:208;a:5:{s:4:"time";d:**********.910118;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:45:"cornernote\workflow\manager\models\Transition";}i:209;a:5:{s:4:"time";d:**********.91014;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:210;a:5:{s:4:"time";d:**********.910839;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:211;a:5:{s:4:"time";d:**********.910864;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:212;a:5:{s:4:"time";d:**********.910885;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:213;a:5:{s:4:"time";d:**********.911662;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:43:"cornernote\workflow\manager\models\Metadata";}i:214;a:5:{s:4:"time";d:**********.911754;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:43:"cornernote\workflow\manager\models\Metadata";}i:215;a:5:{s:4:"time";d:**********.911929;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:216;a:5:{s:4:"time";d:**********.91359;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:217;a:5:{s:4:"time";d:**********.913608;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:218;a:5:{s:4:"time";d:**********.913771;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:219;a:5:{s:4:"time";d:**********.91379;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:220;a:5:{s:4:"time";d:**********.914471;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:221;a:5:{s:4:"time";d:**********.914621;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:222;a:5:{s:4:"time";d:**********.915479;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:223;a:5:{s:4:"time";d:**********.919817;s:4:"name";s:17:"commitTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:224;a:5:{s:4:"time";d:**********.920325;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:225;a:5:{s:4:"time";d:**********.920347;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:46:"backend\controllers\DocumentFeedbackController";}i:226;a:5:{s:4:"time";d:**********.920354;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:227;a:5:{s:4:"time";d:**********.9204;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:228;a:5:{s:4:"time";d:**********.92279;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:229;a:5:{s:4:"time";d:**********.922797;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:230;a:5:{s:4:"time";d:**********.922804;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:231;a:5:{s:4:"time";d:**********.923926;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:232;a:5:{s:4:"time";d:**********.923979;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:**********.686818;s:3:"end";d:**********.932717;s:6:"memory";i:10256832;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:333:"a:3:{s:8:"messages";a:1:{i:36;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.777021;i:4;a:0:{}i:5;i:7323120;}}s:5:"route";s:24:"document-feedback/create";s:6:"action";s:62:"backend\controllers\DocumentFeedbackController::actionCreate()";}";s:7:"request";s:12300:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:19:{s:12:"content-type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:14:"content-length";s:3:"320";s:14:"sec-fetch-dest";s:5:"empty";s:14:"sec-fetch-mode";s:4:"cors";s:14:"sec-fetch-site";s:11:"same-origin";s:6:"origin";s:21:"http://localhost:8005";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"";s:12:"x-csrf-token";s:88:"lqCjbAUBTCCt02BQgZHjjfXBbPJqNEqyZE_bVedFQwnY8Mo9WmA2F_iHV2bywdf4wIwfvTVxBuoUCuMmowwtRA==";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36";s:7:"referer";s:70:"http://localhost:8005/backoffice/index.php?r=document%2Fcomments&id=23";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:957:"sidebar-collapse=false; advanced-frontend-fmz=5aq56c0k9mq2kuj625o5s3h214; _identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; _csrf-frontend=2d4c3047e7a91b1f2885c3537d6571c92c52456f58a3a57ccf3a69a5a789f51ba%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22BXnguNFHn_7mQloD5FR82zMnsqS7ncXT%22%3B%7D; advanced-backend-fmz=jvvvm6hursbt8umpglia4j5spl; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; _csrf-backend=67baeba99e419a42546f2a459a8d5d32745bb89561fdf16aa322cedafd10ea5aa%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22NPiQ_az7UT76sP4u5MsO_ELXpE8sDInM%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:3:"*/*";s:10:"connection";s:10:"keep-alive";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68d53542b96fa";s:16:"X-Debug-Duration";s:3:"238";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=68d53542b96fa";s:10:"Set-Cookie";s:311:"_identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; expires=Sat, 25 Oct 2025 12:27:46 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:24:"document-feedback/create";s:6:"action";s:62:"backend\controllers\DocumentFeedbackController::actionCreate()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:1;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:3:"Raw";s:320:"_csrf-backend=lqCjbAUBTCCt02BQgZHjjfXBbPJqNEqyZE_bVedFQwnY8Mo9WmA2F_iHV2bywdf4wIwfvTVxBuoUCuMmowwtRA%3D%3D&DocumentFeedback%5Bdocument_id%5D=23&redirect_url=%2Fbackoffice%2Findex.php%3Fr%3Ddocument%252Fcomments%26id%3D23&DocumentFeedback%5Bcomment%5D=3test3&redirect_url=/backoffice/index.php?r=document%2Fcomments&id=23";s:7:"Decoded";a:4:{s:13:"_csrf-backend";s:88:"lqCjbAUBTCCt02BQgZHjjfXBbPJqNEqyZE_bVedFQwnY8Mo9WmA2F_iHV2bywdf4wIwfvTVxBuoUCuMmowwtRA==";s:16:"DocumentFeedback";a:2:{s:11:"document_id";s:2:"23";s:7:"comment";s:6:"3test3";}s:12:"redirect_url";s:41:"/backoffice/index.php?r=document/comments";s:2:"id";s:2:"23";}}s:6:"SERVER";a:107:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-61f8f3ab-5e0f-4b03-909c-a3b1f2ce73ff";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:582:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\GitHub CLI\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:50:"/backoffice/index.php?r=document-feedback%2Fcreate";s:14:"REQUEST_METHOD";s:4:"POST";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"49198";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:28:"r=document-feedback%2Fcreate";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:14:"CONTENT_LENGTH";s:3:"320";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:5:"empty";s:19:"HTTP_SEC_FETCH_MODE";s:4:"cors";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:11:"HTTP_ORIGIN";s:21:"http://localhost:8005";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"";s:17:"HTTP_X_CSRF_TOKEN";s:88:"lqCjbAUBTCCt02BQgZHjjfXBbPJqNEqyZE_bVedFQwnY8Mo9WmA2F_iHV2bywdf4wIwfvTVxBuoUCuMmowwtRA==";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36";s:12:"HTTP_REFERER";s:70:"http://localhost:8005/backoffice/index.php?r=document%2Fcomments&id=23";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:957:"sidebar-collapse=false; advanced-frontend-fmz=5aq56c0k9mq2kuj625o5s3h214; _identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; _csrf-frontend=2d4c3047e7a91b1f2885c3537d6571c92c52456f58a3a57ccf3a69a5a789f51ba%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22BXnguNFHn_7mQloD5FR82zMnsqS7ncXT%22%3B%7D; advanced-backend-fmz=jvvvm6hursbt8umpglia4j5spl; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; _csrf-backend=67baeba99e419a42546f2a459a8d5d32745bb89561fdf16aa322cedafd10ea5aa%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22NPiQ_az7UT76sP4u5MsO_ELXpE8sDInM%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:3:"*/*";s:17:"HTTP_CONTENT_TYPE";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:19:"HTTP_CONTENT_LENGTH";s:3:"320";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.677695;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:1:{s:1:"r";s:24:"document-feedback/create";}s:4:"POST";a:4:{s:13:"_csrf-backend";s:88:"lqCjbAUBTCCt02BQgZHjjfXBbPJqNEqyZE_bVedFQwnY8Mo9WmA2F_iHV2bywdf4wIwfvTVxBuoUCuMmowwtRA==";s:16:"DocumentFeedback";a:2:{s:11:"document_id";s:2:"23";s:7:"comment";s:6:"3test3";}s:12:"redirect_url";s:41:"/backoffice/index.php?r=document/comments";s:2:"id";s:2:"23";}s:6:"COOKIE";a:7:{s:16:"sidebar-collapse";s:5:"false";s:21:"advanced-frontend-fmz";s:26:"5aq56c0k9mq2kuj625o5s3h214";s:18:"_identity-frontend";s:158:"ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:14:"_csrf-frontend";s:140:"2d4c3047e7a91b1f2885c3537d6571c92c52456f58a3a57ccf3a69a5a789f51ba:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"BXnguNFHn_7mQloD5FR82zMnsqS7ncXT";}";s:20:"advanced-backend-fmz";s:26:"jvvvm6hursbt8umpglia4j5spl";s:17:"_identity-backend";s:157:"39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:13:"_csrf-backend";s:139:"67baeba99e419a42546f2a459a8d5d32745bb89561fdf16aa322cedafd10ea5aa:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"NPiQ_az7UT76sP4u5MsO_ELXpE8sDInM";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:4:{s:7:"__flash";a:0:{}s:11:"__returnUrl";s:33:"http://localhost:8005/backoffice/";s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"**********";s:10:"updated_at";s:10:"**********";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_**********'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"68d53542b96fa";s:3:"url";s:71:"http://localhost:8005/backoffice/index.php?r=document-feedback%2Fcreate";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";d:**********.677695;s:10:"statusCode";i:200;s:8:"sqlCount";i:88;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10069584;s:14:"processingTime";d:0.24289417266845703;}s:10:"exceptions";a:0:{}}