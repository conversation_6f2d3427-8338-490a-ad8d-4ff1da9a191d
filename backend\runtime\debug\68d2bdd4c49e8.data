a:14:{s:6:"config";s:15911:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:71:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:34:"miloschuman/yii2-highcharts-widget";a:3:{s:4:"name";s:34:"miloschuman/yii2-highcharts-widget";s:7:"version";s:8:"11.0.0.0";s:5:"alias";a:1:{s:23:"@miloschuman/highcharts";s:65:"C:\Web\Reclassering\vendor/miloschuman/yii2-highcharts-widget/src";}}}}";s:3:"log";s:61733:"a:1:{s:8:"messages";a:206:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1758641620.685325;i:4;a:0:{}i:5;i:3024344;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1758641620.686249;i:4;a:0:{}i:5;i:3129536;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1758641620.686256;i:4;a:0:{}i:5;i:3129832;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1758641620.688894;i:4;a:0:{}i:5;i:3159192;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1758641620.689841;i:4;a:0:{}i:5;i:3187304;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1758641620.691019;i:4;a:0:{}i:5;i:3407832;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1758641620.69103;i:4;a:0:{}i:5;i:3408472;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1758641620.701281;i:4;a:0:{}i:5;i:4308160;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.737835;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5632216;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1758641620.737895;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5700032;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.760375;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5757848;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.76368;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5778568;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.783416;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6182848;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1758641620.803827;i:4;a:0:{}i:5;i:6819800;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1758641620.807259;i:4;a:0:{}i:5;i:6999896;}i:25;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1758641620.807294;i:4;a:0:{}i:5;i:7000536;}i:26;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.81025;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7070456;}i:29;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.812989;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7082088;}i:32;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.814258;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7081872;}i:35;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1758641620.828896;i:4;a:0:{}i:5;i:7262264;}i:37;a:6:{i:0;s:35:"Route requested: 'gii/default/view'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1758641620.829062;i:4;a:0:{}i:5;i:7326736;}i:38;a:6:{i:0;s:30:"Route to run: gii/default/view";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1758641620.832273;i:4;a:0:{}i:5;i:7362080;}i:39;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('gii/default/view', '1', '::1', 0, 'POST', '2025-09-23 16:33:40')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1758641620.845553;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7633944;}i:42;a:6:{i:0;s:66:"SELECT * FROM `notification_trigger` WHERE `route`='/default/view'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.874776;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:8625976;}i:45;a:6:{i:0;s:67:"Running action: yii\gii\controllers\DefaultController::actionView()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1758641620.876197;i:4;a:0:{}i:5;i:8620752;}i:46;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `notification_trigger`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.885475;i:4;a:0:{}i:5;i:8749776;}i:49;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.889355;i:4;a:0:{}i:5;i:8756152;}i:52;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_trigger' AND `kcu`.`TABLE_NAME` = 'notification_trigger'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.890533;i:4;a:0:{}i:5;i:8756176;}i:55;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.896428;i:4;a:0:{}i:5;i:8754216;}i:58;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `afspraak`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.899744;i:4;a:0:{}i:5;i:8759160;}i:61;a:6:{i:0;s:28:"SHOW CREATE TABLE `afspraak`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.902184;i:4;a:0:{}i:5;i:8769064;}i:64;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'afspraak' AND `kcu`.`TABLE_NAME` = 'afspraak'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.904341;i:4;a:0:{}i:5;i:8836416;}i:67;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_data`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.906719;i:4;a:0:{}i:5;i:8838912;}i:70;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.909048;i:4;a:0:{}i:5;i:8845000;}i:73;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_data' AND `kcu`.`TABLE_NAME` = 'audit_data'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.910263;i:4;a:0:{}i:5;i:8844880;}i:76;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_error`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.912267;i:4;a:0:{}i:5;i:8845888;}i:79;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_error`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.914239;i:4;a:0:{}i:5;i:8856552;}i:82;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_error' AND `kcu`.`TABLE_NAME` = 'audit_error'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.919783;i:4;a:0:{}i:5;i:8854112;}i:85;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `audit_javascript`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.921606;i:4;a:0:{}i:5;i:8855136;}i:88;a:6:{i:0;s:36:"SHOW CREATE TABLE `audit_javascript`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.923393;i:4;a:0:{}i:5;i:8863032;}i:91;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_javascript' AND `kcu`.`TABLE_NAME` = 'audit_javascript'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.925116;i:4;a:0:{}i:5;i:8861888;}i:94;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_mail`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.92706;i:4;a:0:{}i:5;i:8862904;}i:97;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_mail`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.928801;i:4;a:0:{}i:5;i:8876232;}i:100;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_mail' AND `kcu`.`TABLE_NAME` = 'audit_mail'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.933137;i:4;a:0:{}i:5;i:8872320;}i:103;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_trail`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.934655;i:4;a:0:{}i:5;i:8873648;}i:106;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_trail`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.936106;i:4;a:0:{}i:5;i:8884576;}i:109;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_trail' AND `kcu`.`TABLE_NAME` = 'audit_trail'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.938959;i:4;a:0:{}i:5;i:8881912;}i:112;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `cache`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.940487;i:4;a:0:{}i:5;i:8882912;}i:115;a:6:{i:0;s:25:"SHOW CREATE TABLE `cache`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.941836;i:4;a:0:{}i:5;i:8887216;}i:118;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'cache' AND `kcu`.`TABLE_NAME` = 'cache'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.942446;i:4;a:0:{}i:5;i:8888096;}i:121;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.943791;i:4;a:0:{}i:5;i:8888192;}i:124;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.945264;i:4;a:0:{}i:5;i:8900048;}i:127;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.946205;i:4;a:0:{}i:5;i:8897080;}i:130;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.948149;i:4;a:0:{}i:5;i:8905496;}i:133;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.949459;i:4;a:0:{}i:5;i:8914304;}i:136;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.950448;i:4;a:0:{}i:5;i:8912696;}i:139;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.951885;i:4;a:0:{}i:5;i:8914792;}i:142;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.9532;i:4;a:0:{}i:5;i:8922696;}i:145;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.953751;i:4;a:0:{}i:5;i:8921584;}i:148;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `document_permission`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.955421;i:4;a:0:{}i:5;i:8923688;}i:151;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.956778;i:4;a:0:{}i:5;i:8930632;}i:154;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_permission' AND `kcu`.`TABLE_NAME` = 'document_permission'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.957618;i:4;a:0:{}i:5;i:8930064;}i:157;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.959042;i:4;a:0:{}i:5;i:8931624;}i:160;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.960442;i:4;a:0:{}i:5;i:8942568;}i:163;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.961331;i:4;a:0:{}i:5;i:8940128;}i:166;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.962773;i:4;a:0:{}i:5;i:8941672;}i:169;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.964136;i:4;a:0:{}i:5;i:8950464;}i:172;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.965096;i:4;a:0:{}i:5;i:8948872;}i:175;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `document_upload`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.966726;i:4;a:0:{}i:5;i:8951072;}i:178;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.96814;i:4;a:0:{}i:5;i:8960936;}i:181;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_upload' AND `kcu`.`TABLE_NAME` = 'document_upload'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.968987;i:4;a:0:{}i:5;i:8958976;}i:184;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.97059;i:4;a:0:{}i:5;i:8959992;}i:187;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.971974;i:4;a:0:{}i:5;i:8968672;}i:190;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.972865;i:4;a:0:{}i:5;i:8967176;}i:193;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.974361;i:4;a:0:{}i:5;i:8968720;}i:196;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.975803;i:4;a:0:{}i:5;i:8979744;}i:199;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'functionality' AND `kcu`.`TABLE_NAME` = 'functionality'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.977472;i:4;a:0:{}i:5;i:8977208;}i:202;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `functionality_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.978976;i:4;a:0:{}i:5;i:8979088;}i:205;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.980338;i:4;a:0:{}i:5;i:8987040;}i:208;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'functionality_type' AND `kcu`.`TABLE_NAME` = 'functionality_type'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.981543;i:4;a:0:{}i:5;i:8985880;}i:211;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `gedetineerde`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.983178;i:4;a:0:{}i:5;i:8987424;}i:214;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.984566;i:4;a:0:{}i:5;i:9000168;}i:217;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'gedetineerde' AND `kcu`.`TABLE_NAME` = 'gedetineerde'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.98528;i:4;a:0:{}i:5;i:8996704;}i:220;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `instance_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.9877;i:4;a:0:{}i:5;i:8998232;}i:223;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.988907;i:4;a:0:{}i:5;i:9005480;}i:226;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instance_type' AND `kcu`.`TABLE_NAME` = 'instance_type'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.991856;i:4;a:0:{}i:5;i:9004832;}i:229;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `instantie`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.993062;i:4;a:0:{}i:5;i:9006376;}i:232;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.994251;i:4;a:0:{}i:5;i:9018216;}i:235;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instantie' AND `kcu`.`TABLE_NAME` = 'instantie'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.995547;i:4;a:0:{}i:5;i:9015264;}i:238;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.996927;i:4;a:0:{}i:5;i:9016792;}i:241;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.998741;i:4;a:0:{}i:5;i:9032176;}i:244;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.000421;i:4;a:0:{}i:5;i:9027304;}i:247;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.001842;i:4;a:0:{}i:5;i:9028312;}i:250;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.00288;i:4;a:0:{}i:5;i:9031776;}i:253;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.004105;i:4;a:0:{}i:5;i:9033120;}i:256;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.005436;i:4;a:0:{}i:5;i:9033216;}i:259;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.006719;i:4;a:0:{}i:5;i:9045344;}i:262;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.008226;i:4;a:0:{}i:5;i:9044200;}i:265;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.009761;i:4;a:0:{}i:5;i:9044312;}i:268;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.011079;i:4;a:0:{}i:5;i:9047760;}i:271;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_role' AND `kcu`.`TABLE_NAME` = 'notification_role'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.012009;i:4;a:0:{}i:5;i:9049120;}i:274;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.013414;i:4;a:0:{}i:5;i:9050688;}i:277;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.014699;i:4;a:0:{}i:5;i:9058672;}i:280;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.015845;i:4;a:0:{}i:5;i:9057544;}i:283;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.017197;i:4;a:0:{}i:5;i:9059088;}i:286;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.0185;i:4;a:0:{}i:5;i:9068144;}i:289;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.019118;i:4;a:0:{}i:5;i:9066456;}i:292;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.020352;i:4;a:0:{}i:5;i:9068000;}i:295;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.021634;i:4;a:0:{}i:5;i:9077960;}i:298;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.022237;i:4;a:0:{}i:5;i:9076080;}i:301;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_metadata`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.023212;i:4;a:0:{}i:5;i:9077624;}i:304;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_metadata`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.024463;i:4;a:0:{}i:5;i:9083024;}i:307;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_metadata' AND `kcu`.`TABLE_NAME` = 'sw_metadata'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.025331;i:4;a:0:{}i:5;i:9083232;}i:310;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.026629;i:4;a:0:{}i:5;i:9083328;}i:313;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.027751;i:4;a:0:{}i:5;i:9088640;}i:316;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.028478;i:4;a:0:{}i:5;i:9088928;}i:319;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `sw_transition`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.02975;i:4;a:0:{}i:5;i:9090304;}i:322;a:6:{i:0;s:33:"SHOW CREATE TABLE `sw_transition`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.030841;i:4;a:0:{}i:5;i:9094816;}i:325;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_transition' AND `kcu`.`TABLE_NAME` = 'sw_transition'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.033261;i:4;a:0:{}i:5;i:9095536;}i:328;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_workflow`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.034459;i:4;a:0:{}i:5;i:9095632;}i:331;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_workflow`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.035528;i:4;a:0:{}i:5;i:9099192;}i:334;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_workflow' AND `kcu`.`TABLE_NAME` = 'sw_workflow'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.036505;i:4;a:0:{}i:5;i:9100456;}i:337;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `test`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.03732;i:4;a:0:{}i:5;i:9101184;}i:340;a:6:{i:0;s:24:"SHOW CREATE TABLE `test`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.038516;i:4;a:0:{}i:5;i:9104680;}i:343;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'test' AND `kcu`.`TABLE_NAME` = 'test'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.039146;i:4;a:0:{}i:5;i:9105984;}i:346;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_permission`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.040314;i:4;a:0:{}i:5;i:9106096;}i:349;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_permission`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.041483;i:4;a:0:{}i:5;i:9113296;}i:352;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_permission' AND `kcu`.`TABLE_NAME` = 'user_permission'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.042176;i:4;a:0:{}i:5;i:9112680;}i:355;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.04348;i:4;a:0:{}i:5;i:9113664;}i:358;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.044664;i:4;a:0:{}i:5;i:9120672;}i:361;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.045393;i:4;a:0:{}i:5;i:9120056;}i:364;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.046865;i:4;a:0:{}i:5;i:9125648;}i:367;a:6:{i:0;s:28:"SHOW CREATE TABLE `afspraak`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.051054;i:4;a:0:{}i:5;i:9132976;}i:370;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.055086;i:4;a:0:{}i:5;i:9135456;}i:373;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_error`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.055972;i:4;a:0:{}i:5;i:9137776;}i:376;a:6:{i:0;s:36:"SHOW CREATE TABLE `audit_javascript`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.056403;i:4;a:0:{}i:5;i:9139744;}i:379;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_mail`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.056803;i:4;a:0:{}i:5;i:9141712;}i:382;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_trail`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.057248;i:4;a:0:{}i:5;i:9143976;}i:385;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.057917;i:4;a:0:{}i:5;i:9146320;}i:388;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.058546;i:4;a:0:{}i:5;i:9148208;}i:391;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.059251;i:4;a:0:{}i:5;i:9149792;}i:394;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.05985;i:4;a:0:{}i:5;i:9151680;}i:397;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.060441;i:4;a:0:{}i:5;i:9153664;}i:400;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.061127;i:4;a:0:{}i:5;i:9155248;}i:403;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.061839;i:4;a:0:{}i:5;i:9156856;}i:406;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.06233;i:4;a:0:{}i:5;i:9159240;}i:409;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.062829;i:4;a:0:{}i:5;i:9160824;}i:412;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.063279;i:4;a:0:{}i:5;i:9162424;}i:415;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.063727;i:4;a:0:{}i:5;i:9165128;}i:418;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.064199;i:4;a:0:{}i:5;i:9167072;}i:421;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.064949;i:4;a:0:{}i:5;i:9169720;}i:424;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.065765;i:4;a:0:{}i:5;i:9171296;}i:427;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.066552;i:4;a:0:{}i:5;i:9173192;}i:430;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.066999;i:4;a:0:{}i:5;i:9174768;}i:433;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.067448;i:4;a:0:{}i:5;i:9176320;}i:436;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.067905;i:4;a:0:{}i:5;i:9178328;}i:439;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.068342;i:4;a:0:{}i:5;i:9179928;}i:442;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.068961;i:4;a:0:{}i:5;i:9181936;}i:445;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.069534;i:4;a:0:{}i:5;i:9183512;}i:448;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.069986;i:4;a:0:{}i:5;i:9185528;}i:451;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.070539;i:4;a:0:{}i:5;i:9187768;}i:454;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.071044;i:4;a:0:{}i:5;i:9189336;}i:457;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.071504;i:4;a:0:{}i:5;i:9190864;}i:460;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.071956;i:4;a:0:{}i:5;i:9192832;}i:463;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.072425;i:4;a:0:{}i:5;i:9194408;}i:466;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.072863;i:4;a:0:{}i:5;i:9196376;}i:469;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.073316;i:4;a:0:{}i:5;i:9197912;}i:472;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.073813;i:4;a:0:{}i:5;i:9199840;}i:475;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.074437;i:4;a:0:{}i:5;i:9201920;}i:478;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.074887;i:4;a:0:{}i:5;i:9203888;}i:481;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.075336;i:4;a:0:{}i:5;i:9206280;}i:484;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.075856;i:4;a:0:{}i:5;i:9208296;}i:487;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.076303;i:4;a:0:{}i:5;i:9209856;}i:490;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.076785;i:4;a:0:{}i:5;i:9211392;}i:493;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.07723;i:4;a:0:{}i:5;i:9212888;}i:496;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.077689;i:4;a:0:{}i:5;i:9214864;}i:499;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.078216;i:4;a:0:{}i:5;i:9216464;}i:502;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.078752;i:4;a:0:{}i:5;i:9219456;}i:505;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_permission`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.079562;i:4;a:0:{}i:5;i:9221376;}i:508;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.080465;i:4;a:0:{}i:5;i:9222896;}i:511;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.08119;i:4;a:0:{}i:5;i:9224408;}i:514;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.081645;i:4;a:0:{}i:5;i:9234984;}i:517;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.08205;i:4;a:0:{}i:5;i:9235696;}i:520;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082455;i:4;a:0:{}i:5;i:9236408;}i:523;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082846;i:4;a:0:{}i:5;i:9237120;}i:526;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.083213;i:4;a:0:{}i:5;i:9237832;}i:529;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.08366;i:4;a:0:{}i:5;i:9238544;}i:532;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.084351;i:4;a:0:{}i:5;i:9239256;}i:535;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.084811;i:4;a:0:{}i:5;i:9239968;}i:538;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.085236;i:4;a:0:{}i:5;i:9240680;}i:541;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.085594;i:4;a:0:{}i:5;i:9241392;}i:544;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.086027;i:4;a:0:{}i:5;i:9242104;}i:547;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.086354;i:4;a:0:{}i:5;i:9242808;}i:550;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.086745;i:4;a:0:{}i:5;i:9243520;}i:553;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.087352;i:4;a:0:{}i:5;i:9245056;}i:556;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.087853;i:4;a:0:{}i:5;i:9245760;}i:559;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.088107;i:4;a:0:{}i:5;i:9246472;}i:562;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.088484;i:4;a:0:{}i:5;i:9247176;}i:565;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.089041;i:4;a:0:{}i:5;i:9255248;}i:568;a:6:{i:0;s:103:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2-gii\src\generators\model/default/model.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1758641621.158387;i:4;a:0:{}i:5;i:9408856;}i:569;a:6:{i:0;s:91:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2-gii\src\views\default\view.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1758641621.428432;i:4;a:0:{}i:5;i:9351672;}i:570;a:6:{i:0;s:94:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2-gii\src\generators\model/form.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1758641621.442588;i:4;a:0:{}i:5;i:9906440;}i:571;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2-gii\src\views\default\view/files.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1758641621.472052;i:4;a:0:{}i:5;i:10056656;}i:572;a:6:{i:0;s:96:"Rendering view file: C:\Web\Reclassering\vendor\yiisoft\yii2-gii\src\views\layouts\generator.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1758641621.625437;i:4;a:0:{}i:5;i:10036352;}i:573;a:6:{i:0;s:91:"Rendering view file: C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src/views/layouts/main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1758641621.627879;i:4;a:0:{}i:5;i:10094856;}i:574;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.96063184738159, `memory_max`=10380936 WHERE `id`=5129";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1758641621.63691;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:10155360;}}}";s:9:"profiling";s:116505:"a:3:{s:6:"memory";i:10380936;s:4:"time";d:0.9678668975830078;s:8:"messages";a:370:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1758641620.737916;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5700840;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1758641620.75592;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5744144;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.756008;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5743928;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.760261;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5756560;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.760412;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5758760;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.761534;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5761336;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.763734;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5779608;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.768542;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5782136;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.783502;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6183232;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.784611;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6185600;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.81032;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7071368;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.812923;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7080800;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.813012;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7083000;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.813938;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7084912;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.814305;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7083552;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.816369;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7085408;}i:40;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('gii/default/view', '1', '::1', 0, 'POST', '2025-09-23 16:33:40')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1758641620.845621;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7635304;}i:41;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('gii/default/view', '1', '::1', 0, 'POST', '2025-09-23 16:33:40')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1758641620.852359;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7637096;}i:43;a:6:{i:0;s:66:"SELECT * FROM `notification_trigger` WHERE `route`='/default/view'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.874837;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:8627480;}i:44;a:6:{i:0;s:66:"SELECT * FROM `notification_trigger` WHERE `route`='/default/view'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.876055;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:8628944;}i:47;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `notification_trigger`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.885535;i:4;a:0:{}i:5;i:8750096;}i:48;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `notification_trigger`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.889268;i:4;a:0:{}i:5;i:8755432;}i:50;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.889381;i:4;a:0:{}i:5;i:8756472;}i:51;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.890343;i:4;a:0:{}i:5;i:8757800;}i:53;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_trigger' AND `kcu`.`TABLE_NAME` = 'notification_trigger'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.890559;i:4;a:0:{}i:5;i:8756624;}i:54;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_trigger' AND `kcu`.`TABLE_NAME` = 'notification_trigger'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.893408;i:4;a:0:{}i:5;i:8758592;}i:56;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.896469;i:4;a:0:{}i:5;i:8754536;}i:57;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.899613;i:4;a:0:{}i:5;i:8757688;}i:59;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `afspraak`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.899783;i:4;a:0:{}i:5;i:8759480;}i:60;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `afspraak`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.902132;i:4;a:0:{}i:5;i:8768368;}i:62;a:6:{i:0;s:28:"SHOW CREATE TABLE `afspraak`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.902202;i:4;a:0:{}i:5;i:8769384;}i:63;a:6:{i:0;s:28:"SHOW CREATE TABLE `afspraak`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.903543;i:4;a:0:{}i:5;i:8770704;}i:65;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'afspraak' AND `kcu`.`TABLE_NAME` = 'afspraak'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.904366;i:4;a:0:{}i:5;i:8838144;}i:66;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'afspraak' AND `kcu`.`TABLE_NAME` = 'afspraak'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.906648;i:4;a:0:{}i:5;i:8840096;}i:68;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_data`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.906737;i:4;a:0:{}i:5;i:8839232;}i:69;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_data`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.908996;i:4;a:0:{}i:5;i:8844304;}i:71;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.909065;i:4;a:0:{}i:5;i:8845320;}i:72;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.909999;i:4;a:0:{}i:5;i:8846448;}i:74;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_data' AND `kcu`.`TABLE_NAME` = 'audit_data'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.910302;i:4;a:0:{}i:5;i:8845328;}i:75;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_data' AND `kcu`.`TABLE_NAME` = 'audit_data'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.912212;i:4;a:0:{}i:5;i:8847288;}i:77;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_error`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.91228;i:4;a:0:{}i:5;i:8846208;}i:78;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_error`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.914197;i:4;a:0:{}i:5;i:8855856;}i:80;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_error`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.914254;i:4;a:0:{}i:5;i:8856872;}i:81;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_error`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.919628;i:4;a:0:{}i:5;i:8858192;}i:83;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_error' AND `kcu`.`TABLE_NAME` = 'audit_error'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.919798;i:4;a:0:{}i:5;i:8854560;}i:84;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_error' AND `kcu`.`TABLE_NAME` = 'audit_error'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.921423;i:4;a:0:{}i:5;i:8856520;}i:86;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `audit_javascript`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.921627;i:4;a:0:{}i:5;i:8855456;}i:87;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `audit_javascript`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.92335;i:4;a:0:{}i:5;i:8862328;}i:89;a:6:{i:0;s:36:"SHOW CREATE TABLE `audit_javascript`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.923406;i:4;a:0:{}i:5;i:8863352;}i:90;a:6:{i:0;s:36:"SHOW CREATE TABLE `audit_javascript`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.925007;i:4;a:0:{}i:5;i:8864552;}i:92;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_javascript' AND `kcu`.`TABLE_NAME` = 'audit_javascript'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.925141;i:4;a:0:{}i:5;i:8862336;}i:93;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_javascript' AND `kcu`.`TABLE_NAME` = 'audit_javascript'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.926933;i:4;a:0:{}i:5;i:8864304;}i:95;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_mail`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.927099;i:4;a:0:{}i:5;i:8863224;}i:96;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_mail`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.928773;i:4;a:0:{}i:5;i:8875536;}i:98;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_mail`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.92881;i:4;a:0:{}i:5;i:8876552;}i:99;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_mail`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.933055;i:4;a:0:{}i:5;i:8878000;}i:101;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_mail' AND `kcu`.`TABLE_NAME` = 'audit_mail'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.933146;i:4;a:0:{}i:5;i:8872768;}i:102;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_mail' AND `kcu`.`TABLE_NAME` = 'audit_mail'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.934623;i:4;a:0:{}i:5;i:8874728;}i:104;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_trail`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.934663;i:4;a:0:{}i:5;i:8873968;}i:105;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_trail`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.936082;i:4;a:0:{}i:5;i:8883880;}i:107;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_trail`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.936114;i:4;a:0:{}i:5;i:8884896;}i:108;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_trail`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.938875;i:4;a:0:{}i:5;i:8886344;}i:110;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_trail' AND `kcu`.`TABLE_NAME` = 'audit_trail'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.938969;i:4;a:0:{}i:5;i:8882360;}i:111;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_trail' AND `kcu`.`TABLE_NAME` = 'audit_trail'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.940453;i:4;a:0:{}i:5;i:8884320;}i:113;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `cache`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.940495;i:4;a:0:{}i:5;i:8883232;}i:114;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `cache`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.941813;i:4;a:0:{}i:5;i:8886520;}i:116;a:6:{i:0;s:25:"SHOW CREATE TABLE `cache`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.941844;i:4;a:0:{}i:5;i:8887536;}i:117;a:6:{i:0;s:25:"SHOW CREATE TABLE `cache`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.942392;i:4;a:0:{}i:5;i:8888432;}i:119;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'cache' AND `kcu`.`TABLE_NAME` = 'cache'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.942454;i:4;a:0:{}i:5;i:8888544;}i:120;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'cache' AND `kcu`.`TABLE_NAME` = 'cache'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.943758;i:4;a:0:{}i:5;i:8889808;}i:122;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.9438;i:4;a:0:{}i:5;i:8888512;}i:123;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.94524;i:4;a:0:{}i:5;i:8899352;}i:125;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.945272;i:4;a:0:{}i:5;i:8900368;}i:126;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.94612;i:4;a:0:{}i:5;i:8902584;}i:128;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.946218;i:4;a:0:{}i:5;i:8897528;}i:129;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.948118;i:4;a:0:{}i:5;i:8906720;}i:131;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.948157;i:4;a:0:{}i:5;i:8905816;}i:132;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.949419;i:4;a:0:{}i:5;i:8913600;}i:134;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.949473;i:4;a:0:{}i:5;i:8914624;}i:135;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.950383;i:4;a:0:{}i:5;i:8916336;}i:137;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.950457;i:4;a:0:{}i:5;i:8913144;}i:138;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.951848;i:4;a:0:{}i:5;i:8916176;}i:140;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.951894;i:4;a:0:{}i:5;i:8915112;}i:141;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.953155;i:4;a:0:{}i:5;i:8921992;}i:143;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.95321;i:4;a:0:{}i:5;i:8923016;}i:144;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.953669;i:4;a:0:{}i:5;i:8924728;}i:146;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.95376;i:4;a:0:{}i:5;i:8922032;}i:147;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.955369;i:4;a:0:{}i:5;i:8925072;}i:149;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `document_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.955432;i:4;a:0:{}i:5;i:8924008;}i:150;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `document_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.956741;i:4;a:0:{}i:5;i:8929928;}i:152;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.956789;i:4;a:0:{}i:5;i:8930952;}i:153;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.957538;i:4;a:0:{}i:5;i:8932408;}i:155;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_permission' AND `kcu`.`TABLE_NAME` = 'document_permission'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.957627;i:4;a:0:{}i:5;i:8930512;}i:156;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_permission' AND `kcu`.`TABLE_NAME` = 'document_permission'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.959006;i:4;a:0:{}i:5;i:8933008;}i:158;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.95905;i:4;a:0:{}i:5;i:8931944;}i:159;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.960418;i:4;a:0:{}i:5;i:8941864;}i:161;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.960451;i:4;a:0:{}i:5;i:8942888;}i:162;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.961248;i:4;a:0:{}i:5;i:8944472;}i:164;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.96134;i:4;a:0:{}i:5;i:8940576;}i:165;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.962741;i:4;a:0:{}i:5;i:8943072;}i:167;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.962781;i:4;a:0:{}i:5;i:8941992;}i:168;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.964109;i:4;a:0:{}i:5;i:8949760;}i:170;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.964144;i:4;a:0:{}i:5;i:8950784;}i:171;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.964729;i:4;a:0:{}i:5;i:8952360;}i:173;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.965168;i:4;a:0:{}i:5;i:8949320;}i:174;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.966694;i:4;a:0:{}i:5;i:8951816;}i:176;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `document_upload`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.966734;i:4;a:0:{}i:5;i:8951392;}i:177;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `document_upload`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.968117;i:4;a:0:{}i:5;i:8960232;}i:179;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.968148;i:4;a:0:{}i:5;i:8961256;}i:180;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.968918;i:4;a:0:{}i:5;i:8962704;}i:182;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_upload' AND `kcu`.`TABLE_NAME` = 'document_upload'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.968995;i:4;a:0:{}i:5;i:8959424;}i:183;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_upload' AND `kcu`.`TABLE_NAME` = 'document_upload'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.970547;i:4;a:0:{}i:5;i:8961376;}i:185;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.970604;i:4;a:0:{}i:5;i:8960312;}i:186;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.971949;i:4;a:0:{}i:5;i:8967968;}i:188;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.971982;i:4;a:0:{}i:5;i:8968992;}i:189;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.972793;i:4;a:0:{}i:5;i:8970576;}i:191;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.972874;i:4;a:0:{}i:5;i:8967624;}i:192;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.97433;i:4;a:0:{}i:5;i:8970120;}i:194;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.974369;i:4;a:0:{}i:5;i:8969040;}i:195;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.97578;i:4;a:0:{}i:5;i:8979040;}i:197;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.975811;i:4;a:0:{}i:5;i:8980064;}i:198;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.977396;i:4;a:0:{}i:5;i:8981768;}i:200;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'functionality' AND `kcu`.`TABLE_NAME` = 'functionality'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.977481;i:4;a:0:{}i:5;i:8977656;}i:201;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'functionality' AND `kcu`.`TABLE_NAME` = 'functionality'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.978945;i:4;a:0:{}i:5;i:8980152;}i:203;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `functionality_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.978983;i:4;a:0:{}i:5;i:8979408;}i:204;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `functionality_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.980209;i:4;a:0:{}i:5;i:8986336;}i:206;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.980382;i:4;a:0:{}i:5;i:8987360;}i:207;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.981472;i:4;a:0:{}i:5;i:8988944;}i:209;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'functionality_type' AND `kcu`.`TABLE_NAME` = 'functionality_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.981552;i:4;a:0:{}i:5;i:8986328;}i:210;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'functionality_type' AND `kcu`.`TABLE_NAME` = 'functionality_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.983147;i:4;a:0:{}i:5;i:8988824;}i:212;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.983186;i:4;a:0:{}i:5;i:8987744;}i:213;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.984539;i:4;a:0:{}i:5;i:8999464;}i:215;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.984574;i:4;a:0:{}i:5;i:9000488;}i:216;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.985196;i:4;a:0:{}i:5;i:9002192;}i:218;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'gedetineerde' AND `kcu`.`TABLE_NAME` = 'gedetineerde'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.985293;i:4;a:0:{}i:5;i:8997152;}i:219;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'gedetineerde' AND `kcu`.`TABLE_NAME` = 'gedetineerde'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.987627;i:4;a:0:{}i:5;i:8999632;}i:221;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `instance_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.987715;i:4;a:0:{}i:5;i:8998552;}i:222;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `instance_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.988805;i:4;a:0:{}i:5;i:9004776;}i:224;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.98893;i:4;a:0:{}i:5;i:9005800;}i:225;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.991742;i:4;a:0:{}i:5;i:9007376;}i:227;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instance_type' AND `kcu`.`TABLE_NAME` = 'instance_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.991869;i:4;a:0:{}i:5;i:9005280;}i:228;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instance_type' AND `kcu`.`TABLE_NAME` = 'instance_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.993003;i:4;a:0:{}i:5;i:9007776;}i:230;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.993075;i:4;a:0:{}i:5;i:9006696;}i:231;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.994213;i:4;a:0:{}i:5;i:9017520;}i:233;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.994263;i:4;a:0:{}i:5;i:9018536;}i:234;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.995105;i:4;a:0:{}i:5;i:9020240;}i:236;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instantie' AND `kcu`.`TABLE_NAME` = 'instantie'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.995588;i:4;a:0:{}i:5;i:9015712;}i:237;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instantie' AND `kcu`.`TABLE_NAME` = 'instantie'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.996856;i:4;a:0:{}i:5;i:9018192;}i:239;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.996942;i:4;a:0:{}i:5;i:9017112;}i:240;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.998717;i:4;a:0:{}i:5;i:9031480;}i:242;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.99875;i:4;a:0:{}i:5;i:9032496;}i:243;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.000314;i:4;a:0:{}i:5;i:9034200;}i:245;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.00043;i:4;a:0:{}i:5;i:9027752;}i:246;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.001804;i:4;a:0:{}i:5;i:9029712;}i:248;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.00185;i:4;a:0:{}i:5;i:9028632;}i:249;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.002855;i:4;a:0:{}i:5;i:9031080;}i:251;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.002888;i:4;a:0:{}i:5;i:9032096;}i:252;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.004028;i:4;a:0:{}i:5;i:9033000;}i:254;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.004115;i:4;a:0:{}i:5;i:9033568;}i:255;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.005403;i:4;a:0:{}i:5;i:9034832;}i:257;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.005457;i:4;a:0:{}i:5;i:9037632;}i:258;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.006694;i:4;a:0:{}i:5;i:9044640;}i:260;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.006729;i:4;a:0:{}i:5;i:9045664;}i:261;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.007648;i:4;a:0:{}i:5;i:9046856;}i:263;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.008296;i:4;a:0:{}i:5;i:9044648;}i:264;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.009668;i:4;a:0:{}i:5;i:9045912;}i:266;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.009784;i:4;a:0:{}i:5;i:9044632;}i:267;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.011055;i:4;a:0:{}i:5;i:9047056;}i:269;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.011087;i:4;a:0:{}i:5;i:9048080;}i:270;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.011948;i:4;a:0:{}i:5;i:9049408;}i:272;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_role' AND `kcu`.`TABLE_NAME` = 'notification_role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.012018;i:4;a:0:{}i:5;i:9049568;}i:273;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_role' AND `kcu`.`TABLE_NAME` = 'notification_role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.013382;i:4;a:0:{}i:5;i:9052072;}i:275;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.013422;i:4;a:0:{}i:5;i:9051008;}i:276;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.014675;i:4;a:0:{}i:5;i:9057968;}i:278;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.014708;i:4;a:0:{}i:5;i:9058992;}i:279;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.015772;i:4;a:0:{}i:5;i:9060448;}i:281;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.015853;i:4;a:0:{}i:5;i:9057992;}i:282;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.017166;i:4;a:0:{}i:5;i:9060496;}i:284;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.017206;i:4;a:0:{}i:5;i:9059408;}i:285;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.018476;i:4;a:0:{}i:5;i:9067448;}i:287;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.018508;i:4;a:0:{}i:5;i:9068464;}i:288;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.019031;i:4;a:0:{}i:5;i:9070032;}i:290;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.019127;i:4;a:0:{}i:5;i:9066904;}i:291;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.020122;i:4;a:0:{}i:5;i:9069384;}i:293;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.020373;i:4;a:0:{}i:5;i:9068320;}i:294;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.021592;i:4;a:0:{}i:5;i:9077256;}i:296;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.021648;i:4;a:0:{}i:5;i:9078280;}i:297;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.022089;i:4;a:0:{}i:5;i:9079992;}i:299;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.022248;i:4;a:0:{}i:5;i:9076528;}i:300;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.023175;i:4;a:0:{}i:5;i:9079024;}i:302;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_metadata`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.023221;i:4;a:0:{}i:5;i:9077944;}i:303;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_metadata`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.024439;i:4;a:0:{}i:5;i:9082328;}i:305;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_metadata`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.024471;i:4;a:0:{}i:5;i:9083344;}i:306;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_metadata`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.025268;i:4;a:0:{}i:5;i:9084408;}i:308;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_metadata' AND `kcu`.`TABLE_NAME` = 'sw_metadata'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.02534;i:4;a:0:{}i:5;i:9083680;}i:309;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_metadata' AND `kcu`.`TABLE_NAME` = 'sw_metadata'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.026591;i:4;a:0:{}i:5;i:9084944;}i:311;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.026637;i:4;a:0:{}i:5;i:9083648;}i:312;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.027727;i:4;a:0:{}i:5;i:9087944;}i:314;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.027759;i:4;a:0:{}i:5;i:9088960;}i:315;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.028422;i:4;a:0:{}i:5;i:9090024;}i:317;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.028487;i:4;a:0:{}i:5;i:9089376;}i:318;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.02972;i:4;a:0:{}i:5;i:9090640;}i:320;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `sw_transition`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.029757;i:4;a:0:{}i:5;i:9090624;}i:321;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `sw_transition`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.030819;i:4;a:0:{}i:5;i:9094112;}i:323;a:6:{i:0;s:33:"SHOW CREATE TABLE `sw_transition`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.030855;i:4;a:0:{}i:5;i:9095136;}i:324;a:6:{i:0;s:33:"SHOW CREATE TABLE `sw_transition`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.033208;i:4;a:0:{}i:5;i:9096264;}i:326;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_transition' AND `kcu`.`TABLE_NAME` = 'sw_transition'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.033269;i:4;a:0:{}i:5;i:9095984;}i:327;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_transition' AND `kcu`.`TABLE_NAME` = 'sw_transition'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.034421;i:4;a:0:{}i:5;i:9097248;}i:329;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_workflow`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.034467;i:4;a:0:{}i:5;i:9095952;}i:330;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_workflow`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.035502;i:4;a:0:{}i:5;i:9098496;}i:332;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_workflow`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.035537;i:4;a:0:{}i:5;i:9099512;}i:333;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_workflow`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.036412;i:4;a:0:{}i:5;i:9100512;}i:335;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_workflow' AND `kcu`.`TABLE_NAME` = 'sw_workflow'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.036521;i:4;a:0:{}i:5;i:9100904;}i:336;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_workflow' AND `kcu`.`TABLE_NAME` = 'sw_workflow'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.037277;i:4;a:0:{}i:5;i:9102168;}i:338;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `test`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.037329;i:4;a:0:{}i:5;i:9101504;}i:339;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `test`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.038491;i:4;a:0:{}i:5;i:9103984;}i:341;a:6:{i:0;s:24:"SHOW CREATE TABLE `test`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.038524;i:4;a:0:{}i:5;i:9105000;}i:342;a:6:{i:0;s:24:"SHOW CREATE TABLE `test`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.039093;i:4;a:0:{}i:5;i:9105896;}i:344;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'test' AND `kcu`.`TABLE_NAME` = 'test'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.039154;i:4;a:0:{}i:5;i:9106432;}i:345;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'test' AND `kcu`.`TABLE_NAME` = 'test'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.040284;i:4;a:0:{}i:5;i:9107696;}i:347;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.040322;i:4;a:0:{}i:5;i:9106416;}i:348;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.04146;i:4;a:0:{}i:5;i:9112592;}i:350;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.041491;i:4;a:0:{}i:5;i:9113616;}i:351;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.042105;i:4;a:0:{}i:5;i:9114936;}i:353;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_permission' AND `kcu`.`TABLE_NAME` = 'user_permission'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.042185;i:4;a:0:{}i:5;i:9113128;}i:354;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_permission' AND `kcu`.`TABLE_NAME` = 'user_permission'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.043445;i:4;a:0:{}i:5;i:9115072;}i:356;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.043488;i:4;a:0:{}i:5;i:9113984;}i:357;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.04464;i:4;a:0:{}i:5;i:9119976;}i:359;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.044682;i:4;a:0:{}i:5;i:9120992;}i:360;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.045322;i:4;a:0:{}i:5;i:9122432;}i:362;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.045402;i:4;a:0:{}i:5;i:9120504;}i:363;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.046714;i:4;a:0:{}i:5;i:9122984;}i:365;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.046875;i:4;a:0:{}i:5;i:9125968;}i:366;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.05089;i:4;a:0:{}i:5;i:9152400;}i:368;a:6:{i:0;s:28:"SHOW CREATE TABLE `afspraak`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.051065;i:4;a:0:{}i:5;i:9132976;}i:369;a:6:{i:0;s:28:"SHOW CREATE TABLE `afspraak`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.051623;i:4;a:0:{}i:5;i:9134248;}i:371;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.055132;i:4;a:0:{}i:5;i:9135456;}i:372;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.055837;i:4;a:0:{}i:5;i:9136584;}i:374;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_error`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.055981;i:4;a:0:{}i:5;i:9137776;}i:375;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_error`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.056308;i:4;a:0:{}i:5;i:9139096;}i:377;a:6:{i:0;s:36:"SHOW CREATE TABLE `audit_javascript`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.056411;i:4;a:0:{}i:5;i:9139744;}i:378;a:6:{i:0;s:36:"SHOW CREATE TABLE `audit_javascript`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.056713;i:4;a:0:{}i:5;i:9140944;}i:380;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_mail`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.056811;i:4;a:0:{}i:5;i:9141712;}i:381;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_mail`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.05716;i:4;a:0:{}i:5;i:9143160;}i:383;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_trail`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.057257;i:4;a:0:{}i:5;i:9143976;}i:384;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_trail`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.057826;i:4;a:0:{}i:5;i:9145424;}i:386;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.057925;i:4;a:0:{}i:5;i:9146320;}i:387;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.058464;i:4;a:0:{}i:5;i:9148536;}i:389;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.058554;i:4;a:0:{}i:5;i:9148208;}i:390;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.059084;i:4;a:0:{}i:5;i:9150424;}i:392;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.059264;i:4;a:0:{}i:5;i:9149792;}i:393;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.059766;i:4;a:0:{}i:5;i:9152008;}i:395;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.059858;i:4;a:0:{}i:5;i:9151680;}i:396;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.060344;i:4;a:0:{}i:5;i:9153896;}i:398;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.060449;i:4;a:0:{}i:5;i:9153664;}i:399;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.061047;i:4;a:0:{}i:5;i:9155376;}i:401;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.061135;i:4;a:0:{}i:5;i:9155248;}i:402;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.061756;i:4;a:0:{}i:5;i:9156960;}i:404;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.061846;i:4;a:0:{}i:5;i:9156856;}i:405;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.062243;i:4;a:0:{}i:5;i:9158568;}i:407;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.062337;i:4;a:0:{}i:5;i:9159240;}i:408;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.062742;i:4;a:0:{}i:5;i:9160952;}i:410;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.062837;i:4;a:0:{}i:5;i:9160824;}i:411;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.063171;i:4;a:0:{}i:5;i:9162536;}i:413;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.063286;i:4;a:0:{}i:5;i:9162424;}i:414;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.063625;i:4;a:0:{}i:5;i:9164136;}i:416;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.063735;i:4;a:0:{}i:5;i:9165128;}i:417;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.064121;i:4;a:0:{}i:5;i:9166584;}i:419;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.064207;i:4;a:0:{}i:5;i:9167072;}i:420;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.064494;i:4;a:0:{}i:5;i:9168528;}i:422;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.065036;i:4;a:0:{}i:5;i:9169720;}i:423;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.065654;i:4;a:0:{}i:5;i:9171304;}i:425;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.065779;i:4;a:0:{}i:5;i:9171296;}i:426;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.066473;i:4;a:0:{}i:5;i:9172880;}i:428;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.066559;i:4;a:0:{}i:5;i:9173192;}i:429;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.066921;i:4;a:0:{}i:5;i:9174768;}i:431;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.067006;i:4;a:0:{}i:5;i:9174768;}i:432;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.067371;i:4;a:0:{}i:5;i:9176344;}i:434;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.067464;i:4;a:0:{}i:5;i:9176320;}i:435;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.067822;i:4;a:0:{}i:5;i:9177768;}i:437;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.067913;i:4;a:0:{}i:5;i:9178328;}i:438;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.068238;i:4;a:0:{}i:5;i:9179912;}i:440;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.06835;i:4;a:0:{}i:5;i:9179928;}i:441;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.068794;i:4;a:0:{}i:5;i:9181512;}i:443;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.069002;i:4;a:0:{}i:5;i:9181936;}i:444;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.069408;i:4;a:0:{}i:5;i:9183640;}i:446;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.069544;i:4;a:0:{}i:5;i:9183512;}i:447;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.069893;i:4;a:0:{}i:5;i:9185216;}i:449;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.069994;i:4;a:0:{}i:5;i:9185528;}i:450;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.07041;i:4;a:0:{}i:5;i:9187112;}i:452;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.070546;i:4;a:0:{}i:5;i:9187768;}i:453;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.070962;i:4;a:0:{}i:5;i:9189352;}i:455;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.071052;i:4;a:0:{}i:5;i:9189336;}i:456;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.071426;i:4;a:0:{}i:5;i:9191040;}i:458;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.071512;i:4;a:0:{}i:5;i:9190864;}i:459;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.07184;i:4;a:0:{}i:5;i:9192568;}i:461;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.071963;i:4;a:0:{}i:5;i:9192832;}i:462;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.072347;i:4;a:0:{}i:5;i:9194408;}i:464;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.072433;i:4;a:0:{}i:5;i:9194408;}i:465;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.072754;i:4;a:0:{}i:5;i:9195984;}i:467;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.072871;i:4;a:0:{}i:5;i:9196376;}i:468;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.073238;i:4;a:0:{}i:5;i:9198080;}i:470;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.073324;i:4;a:0:{}i:5;i:9197912;}i:471;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.07372;i:4;a:0:{}i:5;i:9199616;}i:473;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.073821;i:4;a:0:{}i:5;i:9199840;}i:474;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.074348;i:4;a:0:{}i:5;i:9201544;}i:476;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.074445;i:4;a:0:{}i:5;i:9201920;}i:477;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.0748;i:4;a:0:{}i:5;i:9203248;}i:479;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.074895;i:4;a:0:{}i:5;i:9203888;}i:480;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.075257;i:4;a:0:{}i:5;i:9205216;}i:482;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.075344;i:4;a:0:{}i:5;i:9206280;}i:483;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.075759;i:4;a:0:{}i:5;i:9207608;}i:485;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.075863;i:4;a:0:{}i:5;i:9208296;}i:486;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.076226;i:4;a:0:{}i:5;i:9209752;}i:488;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.076311;i:4;a:0:{}i:5;i:9209856;}i:489;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.076706;i:4;a:0:{}i:5;i:9211312;}i:491;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.076792;i:4;a:0:{}i:5;i:9211392;}i:492;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.077152;i:4;a:0:{}i:5;i:9212960;}i:494;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.077238;i:4;a:0:{}i:5;i:9212888;}i:495;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.077594;i:4;a:0:{}i:5;i:9214456;}i:497;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.077697;i:4;a:0:{}i:5;i:9214864;}i:498;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.078142;i:4;a:0:{}i:5;i:9216576;}i:500;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.078224;i:4;a:0:{}i:5;i:9216464;}i:501;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.078661;i:4;a:0:{}i:5;i:9218176;}i:503;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.078759;i:4;a:0:{}i:5;i:9219456;}i:504;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.079071;i:4;a:0:{}i:5;i:9221408;}i:506;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.0796;i:4;a:0:{}i:5;i:9221376;}i:507;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.080341;i:4;a:0:{}i:5;i:9222696;}i:509;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.080489;i:4;a:0:{}i:5;i:9222896;}i:510;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.081114;i:4;a:0:{}i:5;i:9224336;}i:512;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.081201;i:4;a:0:{}i:5;i:9224408;}i:513;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.081564;i:4;a:0:{}i:5;i:9234040;}i:515;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.081653;i:4;a:0:{}i:5;i:9234984;}i:516;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082016;i:4;a:0:{}i:5;i:9237200;}i:518;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082057;i:4;a:0:{}i:5;i:9235696;}i:519;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082425;i:4;a:0:{}i:5;i:9237408;}i:521;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082463;i:4;a:0:{}i:5;i:9236408;}i:522;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082816;i:4;a:0:{}i:5;i:9238120;}i:524;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082854;i:4;a:0:{}i:5;i:9237120;}i:525;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.083185;i:4;a:0:{}i:5;i:9238576;}i:527;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.083221;i:4;a:0:{}i:5;i:9237832;}i:528;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.083526;i:4;a:0:{}i:5;i:9239416;}i:530;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.083698;i:4;a:0:{}i:5;i:9238544;}i:531;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.084321;i:4;a:0:{}i:5;i:9240120;}i:533;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.084359;i:4;a:0:{}i:5;i:9239256;}i:534;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.084782;i:4;a:0:{}i:5;i:9240840;}i:536;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.084818;i:4;a:0:{}i:5;i:9239968;}i:537;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.085207;i:4;a:0:{}i:5;i:9241672;}i:539;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.085244;i:4;a:0:{}i:5;i:9240680;}i:540;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.085563;i:4;a:0:{}i:5;i:9242264;}i:542;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.085602;i:4;a:0:{}i:5;i:9241392;}i:543;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.085971;i:4;a:0:{}i:5;i:9243096;}i:545;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.086034;i:4;a:0:{}i:5;i:9242104;}i:546;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.086323;i:4;a:0:{}i:5;i:9243680;}i:548;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.086362;i:4;a:0:{}i:5;i:9242808;}i:549;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.086714;i:4;a:0:{}i:5;i:9244512;}i:551;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.086768;i:4;a:0:{}i:5;i:9243520;}i:552;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.087034;i:4;a:0:{}i:5;i:9244848;}i:554;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.087362;i:4;a:0:{}i:5;i:9245056;}i:555;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.08782;i:4;a:0:{}i:5;i:9246512;}i:557;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.087861;i:4;a:0:{}i:5;i:9245760;}i:558;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.088073;i:4;a:0:{}i:5;i:9247328;}i:560;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.088115;i:4;a:0:{}i:5;i:9246472;}i:561;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.088431;i:4;a:0:{}i:5;i:9248184;}i:563;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.088492;i:4;a:0:{}i:5;i:9247176;}i:564;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.088846;i:4;a:0:{}i:5;i:9248616;}i:566;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.089049;i:4;a:0:{}i:5;i:9255568;}i:567;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.089393;i:4;a:0:{}i:5;i:9256896;}i:575;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.96063184738159, `memory_max`=10380936 WHERE `id`=5129";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1758641621.636959;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:10156704;}i:576;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.96063184738159, `memory_max`=10380936 WHERE `id`=5129";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1758641621.640951;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:10158104;}}}";s:2:"db";s:115736:"a:1:{s:8:"messages";a:368:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.756008;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5743928;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.760261;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5756560;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.760412;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5758760;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.761534;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5761336;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.763734;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5779608;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.768542;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5782136;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.783502;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6183232;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.784611;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6185600;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.81032;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7071368;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.812923;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7080800;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.813012;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7083000;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.813938;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7084912;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.814305;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7083552;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.816369;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7085408;}i:40;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('gii/default/view', '1', '::1', 0, 'POST', '2025-09-23 16:33:40')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1758641620.845621;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7635304;}i:41;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('gii/default/view', '1', '::1', 0, 'POST', '2025-09-23 16:33:40')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1758641620.852359;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7637096;}i:43;a:6:{i:0;s:66:"SELECT * FROM `notification_trigger` WHERE `route`='/default/view'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.874837;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:8627480;}i:44;a:6:{i:0;s:66:"SELECT * FROM `notification_trigger` WHERE `route`='/default/view'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.876055;i:4;a:2:{i:0;a:5:{s:4:"file";s:61:"C:\Web\Reclassering\common\components\NotificationManager.php";s:4:"line";i:23;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:47:"C:\Web\Reclassering\common\config\bootstrap.php";s:4:"line";i:26;s:8:"function";s:15:"checkAndTrigger";s:5:"class";s:37:"common\components\NotificationManager";s:4:"type";s:2:"->";}}i:5;i:8628944;}i:47;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `notification_trigger`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.885535;i:4;a:0:{}i:5;i:8750096;}i:48;a:6:{i:0;s:45:"SHOW FULL COLUMNS FROM `notification_trigger`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.889268;i:4;a:0:{}i:5;i:8755432;}i:50;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.889381;i:4;a:0:{}i:5;i:8756472;}i:51;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.890343;i:4;a:0:{}i:5;i:8757800;}i:53;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_trigger' AND `kcu`.`TABLE_NAME` = 'notification_trigger'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.890559;i:4;a:0:{}i:5;i:8756624;}i:54;a:6:{i:0;s:796:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_trigger' AND `kcu`.`TABLE_NAME` = 'notification_trigger'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.893408;i:4;a:0:{}i:5;i:8758592;}i:56;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.896469;i:4;a:0:{}i:5;i:8754536;}i:57;a:6:{i:0;s:11:"SHOW TABLES";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.899613;i:4;a:0:{}i:5;i:8757688;}i:59;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `afspraak`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.899783;i:4;a:0:{}i:5;i:8759480;}i:60;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `afspraak`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.902132;i:4;a:0:{}i:5;i:8768368;}i:62;a:6:{i:0;s:28:"SHOW CREATE TABLE `afspraak`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.902202;i:4;a:0:{}i:5;i:8769384;}i:63;a:6:{i:0;s:28:"SHOW CREATE TABLE `afspraak`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.903543;i:4;a:0:{}i:5;i:8770704;}i:65;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'afspraak' AND `kcu`.`TABLE_NAME` = 'afspraak'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.904366;i:4;a:0:{}i:5;i:8838144;}i:66;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'afspraak' AND `kcu`.`TABLE_NAME` = 'afspraak'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.906648;i:4;a:0:{}i:5;i:8840096;}i:68;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_data`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.906737;i:4;a:0:{}i:5;i:8839232;}i:69;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_data`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.908996;i:4;a:0:{}i:5;i:8844304;}i:71;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.909065;i:4;a:0:{}i:5;i:8845320;}i:72;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.909999;i:4;a:0:{}i:5;i:8846448;}i:74;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_data' AND `kcu`.`TABLE_NAME` = 'audit_data'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.910302;i:4;a:0:{}i:5;i:8845328;}i:75;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_data' AND `kcu`.`TABLE_NAME` = 'audit_data'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.912212;i:4;a:0:{}i:5;i:8847288;}i:77;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_error`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.91228;i:4;a:0:{}i:5;i:8846208;}i:78;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_error`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.914197;i:4;a:0:{}i:5;i:8855856;}i:80;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_error`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.914254;i:4;a:0:{}i:5;i:8856872;}i:81;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_error`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.919628;i:4;a:0:{}i:5;i:8858192;}i:83;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_error' AND `kcu`.`TABLE_NAME` = 'audit_error'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.919798;i:4;a:0:{}i:5;i:8854560;}i:84;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_error' AND `kcu`.`TABLE_NAME` = 'audit_error'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.921423;i:4;a:0:{}i:5;i:8856520;}i:86;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `audit_javascript`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.921627;i:4;a:0:{}i:5;i:8855456;}i:87;a:6:{i:0;s:41:"SHOW FULL COLUMNS FROM `audit_javascript`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.92335;i:4;a:0:{}i:5;i:8862328;}i:89;a:6:{i:0;s:36:"SHOW CREATE TABLE `audit_javascript`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.923406;i:4;a:0:{}i:5;i:8863352;}i:90;a:6:{i:0;s:36:"SHOW CREATE TABLE `audit_javascript`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.925007;i:4;a:0:{}i:5;i:8864552;}i:92;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_javascript' AND `kcu`.`TABLE_NAME` = 'audit_javascript'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.925141;i:4;a:0:{}i:5;i:8862336;}i:93;a:6:{i:0;s:788:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_javascript' AND `kcu`.`TABLE_NAME` = 'audit_javascript'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.926933;i:4;a:0:{}i:5;i:8864304;}i:95;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_mail`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.927099;i:4;a:0:{}i:5;i:8863224;}i:96;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_mail`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.928773;i:4;a:0:{}i:5;i:8875536;}i:98;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_mail`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.92881;i:4;a:0:{}i:5;i:8876552;}i:99;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_mail`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.933055;i:4;a:0:{}i:5;i:8878000;}i:101;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_mail' AND `kcu`.`TABLE_NAME` = 'audit_mail'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.933146;i:4;a:0:{}i:5;i:8872768;}i:102;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_mail' AND `kcu`.`TABLE_NAME` = 'audit_mail'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.934623;i:4;a:0:{}i:5;i:8874728;}i:104;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_trail`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.934663;i:4;a:0:{}i:5;i:8873968;}i:105;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_trail`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.936082;i:4;a:0:{}i:5;i:8883880;}i:107;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_trail`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.936114;i:4;a:0:{}i:5;i:8884896;}i:108;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_trail`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.938875;i:4;a:0:{}i:5;i:8886344;}i:110;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_trail' AND `kcu`.`TABLE_NAME` = 'audit_trail'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.938969;i:4;a:0:{}i:5;i:8882360;}i:111;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_trail' AND `kcu`.`TABLE_NAME` = 'audit_trail'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.940453;i:4;a:0:{}i:5;i:8884320;}i:113;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `cache`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.940495;i:4;a:0:{}i:5;i:8883232;}i:114;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `cache`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.941813;i:4;a:0:{}i:5;i:8886520;}i:116;a:6:{i:0;s:25:"SHOW CREATE TABLE `cache`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.941844;i:4;a:0:{}i:5;i:8887536;}i:117;a:6:{i:0;s:25:"SHOW CREATE TABLE `cache`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.942392;i:4;a:0:{}i:5;i:8888432;}i:119;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'cache' AND `kcu`.`TABLE_NAME` = 'cache'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.942454;i:4;a:0:{}i:5;i:8888544;}i:120;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'cache' AND `kcu`.`TABLE_NAME` = 'cache'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.943758;i:4;a:0:{}i:5;i:8889808;}i:122;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.9438;i:4;a:0:{}i:5;i:8888512;}i:123;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.94524;i:4;a:0:{}i:5;i:8899352;}i:125;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.945272;i:4;a:0:{}i:5;i:8900368;}i:126;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.94612;i:4;a:0:{}i:5;i:8902584;}i:128;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.946218;i:4;a:0:{}i:5;i:8897528;}i:129;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.948118;i:4;a:0:{}i:5;i:8906720;}i:131;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.948157;i:4;a:0:{}i:5;i:8905816;}i:132;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.949419;i:4;a:0:{}i:5;i:8913600;}i:134;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.949473;i:4;a:0:{}i:5;i:8914624;}i:135;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.950383;i:4;a:0:{}i:5;i:8916336;}i:137;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.950457;i:4;a:0:{}i:5;i:8913144;}i:138;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.951848;i:4;a:0:{}i:5;i:8916176;}i:140;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.951894;i:4;a:0:{}i:5;i:8915112;}i:141;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.953155;i:4;a:0:{}i:5;i:8921992;}i:143;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.95321;i:4;a:0:{}i:5;i:8923016;}i:144;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.953669;i:4;a:0:{}i:5;i:8924728;}i:146;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.95376;i:4;a:0:{}i:5;i:8922032;}i:147;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.955369;i:4;a:0:{}i:5;i:8925072;}i:149;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `document_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.955432;i:4;a:0:{}i:5;i:8924008;}i:150;a:6:{i:0;s:44:"SHOW FULL COLUMNS FROM `document_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.956741;i:4;a:0:{}i:5;i:8929928;}i:152;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.956789;i:4;a:0:{}i:5;i:8930952;}i:153;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.957538;i:4;a:0:{}i:5;i:8932408;}i:155;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_permission' AND `kcu`.`TABLE_NAME` = 'document_permission'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.957627;i:4;a:0:{}i:5;i:8930512;}i:156;a:6:{i:0;s:794:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_permission' AND `kcu`.`TABLE_NAME` = 'document_permission'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.959006;i:4;a:0:{}i:5;i:8933008;}i:158;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.95905;i:4;a:0:{}i:5;i:8931944;}i:159;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.960418;i:4;a:0:{}i:5;i:8941864;}i:161;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.960451;i:4;a:0:{}i:5;i:8942888;}i:162;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.961248;i:4;a:0:{}i:5;i:8944472;}i:164;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.96134;i:4;a:0:{}i:5;i:8940576;}i:165;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.962741;i:4;a:0:{}i:5;i:8943072;}i:167;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.962781;i:4;a:0:{}i:5;i:8941992;}i:168;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.964109;i:4;a:0:{}i:5;i:8949760;}i:170;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.964144;i:4;a:0:{}i:5;i:8950784;}i:171;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.964729;i:4;a:0:{}i:5;i:8952360;}i:173;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.965168;i:4;a:0:{}i:5;i:8949320;}i:174;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.966694;i:4;a:0:{}i:5;i:8951816;}i:176;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `document_upload`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.966734;i:4;a:0:{}i:5;i:8951392;}i:177;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `document_upload`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.968117;i:4;a:0:{}i:5;i:8960232;}i:179;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.968148;i:4;a:0:{}i:5;i:8961256;}i:180;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.968918;i:4;a:0:{}i:5;i:8962704;}i:182;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_upload' AND `kcu`.`TABLE_NAME` = 'document_upload'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.968995;i:4;a:0:{}i:5;i:8959424;}i:183;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_upload' AND `kcu`.`TABLE_NAME` = 'document_upload'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.970547;i:4;a:0:{}i:5;i:8961376;}i:185;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.970604;i:4;a:0:{}i:5;i:8960312;}i:186;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.971949;i:4;a:0:{}i:5;i:8967968;}i:188;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.971982;i:4;a:0:{}i:5;i:8968992;}i:189;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.972793;i:4;a:0:{}i:5;i:8970576;}i:191;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.972874;i:4;a:0:{}i:5;i:8967624;}i:192;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.97433;i:4;a:0:{}i:5;i:8970120;}i:194;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.974369;i:4;a:0:{}i:5;i:8969040;}i:195;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.97578;i:4;a:0:{}i:5;i:8979040;}i:197;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.975811;i:4;a:0:{}i:5;i:8980064;}i:198;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.977396;i:4;a:0:{}i:5;i:8981768;}i:200;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'functionality' AND `kcu`.`TABLE_NAME` = 'functionality'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.977481;i:4;a:0:{}i:5;i:8977656;}i:201;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'functionality' AND `kcu`.`TABLE_NAME` = 'functionality'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.978945;i:4;a:0:{}i:5;i:8980152;}i:203;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `functionality_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.978983;i:4;a:0:{}i:5;i:8979408;}i:204;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `functionality_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.980209;i:4;a:0:{}i:5;i:8986336;}i:206;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.980382;i:4;a:0:{}i:5;i:8987360;}i:207;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.981472;i:4;a:0:{}i:5;i:8988944;}i:209;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'functionality_type' AND `kcu`.`TABLE_NAME` = 'functionality_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.981552;i:4;a:0:{}i:5;i:8986328;}i:210;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'functionality_type' AND `kcu`.`TABLE_NAME` = 'functionality_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.983147;i:4;a:0:{}i:5;i:8988824;}i:212;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.983186;i:4;a:0:{}i:5;i:8987744;}i:213;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.984539;i:4;a:0:{}i:5;i:8999464;}i:215;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.984574;i:4;a:0:{}i:5;i:9000488;}i:216;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.985196;i:4;a:0:{}i:5;i:9002192;}i:218;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'gedetineerde' AND `kcu`.`TABLE_NAME` = 'gedetineerde'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.985293;i:4;a:0:{}i:5;i:8997152;}i:219;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'gedetineerde' AND `kcu`.`TABLE_NAME` = 'gedetineerde'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.987627;i:4;a:0:{}i:5;i:8999632;}i:221;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `instance_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.987715;i:4;a:0:{}i:5;i:8998552;}i:222;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `instance_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.988805;i:4;a:0:{}i:5;i:9004776;}i:224;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.98893;i:4;a:0:{}i:5;i:9005800;}i:225;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.991742;i:4;a:0:{}i:5;i:9007376;}i:227;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instance_type' AND `kcu`.`TABLE_NAME` = 'instance_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.991869;i:4;a:0:{}i:5;i:9005280;}i:228;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instance_type' AND `kcu`.`TABLE_NAME` = 'instance_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.993003;i:4;a:0:{}i:5;i:9007776;}i:230;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.993075;i:4;a:0:{}i:5;i:9006696;}i:231;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.994213;i:4;a:0:{}i:5;i:9017520;}i:233;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.994263;i:4;a:0:{}i:5;i:9018536;}i:234;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.995105;i:4;a:0:{}i:5;i:9020240;}i:236;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instantie' AND `kcu`.`TABLE_NAME` = 'instantie'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.995588;i:4;a:0:{}i:5;i:9015712;}i:237;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'instantie' AND `kcu`.`TABLE_NAME` = 'instantie'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.996856;i:4;a:0:{}i:5;i:9018192;}i:239;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.996942;i:4;a:0:{}i:5;i:9017112;}i:240;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.998717;i:4;a:0:{}i:5;i:9031480;}i:242;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641620.99875;i:4;a:0:{}i:5;i:9032496;}i:243;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.000314;i:4;a:0:{}i:5;i:9034200;}i:245;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.00043;i:4;a:0:{}i:5;i:9027752;}i:246;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.001804;i:4;a:0:{}i:5;i:9029712;}i:248;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.00185;i:4;a:0:{}i:5;i:9028632;}i:249;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.002855;i:4;a:0:{}i:5;i:9031080;}i:251;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.002888;i:4;a:0:{}i:5;i:9032096;}i:252;a:6:{i:0;s:29:"SHOW CREATE TABLE `migration`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.004028;i:4;a:0:{}i:5;i:9033000;}i:254;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.004115;i:4;a:0:{}i:5;i:9033568;}i:255;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'migration' AND `kcu`.`TABLE_NAME` = 'migration'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.005403;i:4;a:0:{}i:5;i:9034832;}i:257;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.005457;i:4;a:0:{}i:5;i:9037632;}i:258;a:6:{i:0;s:37:"SHOW FULL COLUMNS FROM `notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.006694;i:4;a:0:{}i:5;i:9044640;}i:260;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.006729;i:4;a:0:{}i:5;i:9045664;}i:261;a:6:{i:0;s:32:"SHOW CREATE TABLE `notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.007648;i:4;a:0:{}i:5;i:9046856;}i:263;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.008296;i:4;a:0:{}i:5;i:9044648;}i:264;a:6:{i:0;s:780:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification' AND `kcu`.`TABLE_NAME` = 'notification'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.009668;i:4;a:0:{}i:5;i:9045912;}i:266;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.009784;i:4;a:0:{}i:5;i:9044632;}i:267;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.011055;i:4;a:0:{}i:5;i:9047056;}i:269;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.011087;i:4;a:0:{}i:5;i:9048080;}i:270;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.011948;i:4;a:0:{}i:5;i:9049408;}i:272;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_role' AND `kcu`.`TABLE_NAME` = 'notification_role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.012018;i:4;a:0:{}i:5;i:9049568;}i:273;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_role' AND `kcu`.`TABLE_NAME` = 'notification_role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.013382;i:4;a:0:{}i:5;i:9052072;}i:275;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.013422;i:4;a:0:{}i:5;i:9051008;}i:276;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.014675;i:4;a:0:{}i:5;i:9057968;}i:278;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.014708;i:4;a:0:{}i:5;i:9058992;}i:279;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.015772;i:4;a:0:{}i:5;i:9060448;}i:281;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.015853;i:4;a:0:{}i:5;i:9057992;}i:282;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.017166;i:4;a:0:{}i:5;i:9060496;}i:284;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.017206;i:4;a:0:{}i:5;i:9059408;}i:285;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.018476;i:4;a:0:{}i:5;i:9067448;}i:287;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.018508;i:4;a:0:{}i:5;i:9068464;}i:288;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.019031;i:4;a:0:{}i:5;i:9070032;}i:290;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.019127;i:4;a:0:{}i:5;i:9066904;}i:291;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role' AND `kcu`.`TABLE_NAME` = 'role'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.020122;i:4;a:0:{}i:5;i:9069384;}i:293;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.020373;i:4;a:0:{}i:5;i:9068320;}i:294;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.021592;i:4;a:0:{}i:5;i:9077256;}i:296;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.021648;i:4;a:0:{}i:5;i:9078280;}i:297;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.022089;i:4;a:0:{}i:5;i:9079992;}i:299;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.022248;i:4;a:0:{}i:5;i:9076528;}i:300;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.023175;i:4;a:0:{}i:5;i:9079024;}i:302;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_metadata`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.023221;i:4;a:0:{}i:5;i:9077944;}i:303;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_metadata`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.024439;i:4;a:0:{}i:5;i:9082328;}i:305;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_metadata`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.024471;i:4;a:0:{}i:5;i:9083344;}i:306;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_metadata`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.025268;i:4;a:0:{}i:5;i:9084408;}i:308;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_metadata' AND `kcu`.`TABLE_NAME` = 'sw_metadata'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.02534;i:4;a:0:{}i:5;i:9083680;}i:309;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_metadata' AND `kcu`.`TABLE_NAME` = 'sw_metadata'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.026591;i:4;a:0:{}i:5;i:9084944;}i:311;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.026637;i:4;a:0:{}i:5;i:9083648;}i:312;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.027727;i:4;a:0:{}i:5;i:9087944;}i:314;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.027759;i:4;a:0:{}i:5;i:9088960;}i:315;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.028422;i:4;a:0:{}i:5;i:9090024;}i:317;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.028487;i:4;a:0:{}i:5;i:9089376;}i:318;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.02972;i:4;a:0:{}i:5;i:9090640;}i:320;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `sw_transition`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.029757;i:4;a:0:{}i:5;i:9090624;}i:321;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `sw_transition`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.030819;i:4;a:0:{}i:5;i:9094112;}i:323;a:6:{i:0;s:33:"SHOW CREATE TABLE `sw_transition`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.030855;i:4;a:0:{}i:5;i:9095136;}i:324;a:6:{i:0;s:33:"SHOW CREATE TABLE `sw_transition`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.033208;i:4;a:0:{}i:5;i:9096264;}i:326;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_transition' AND `kcu`.`TABLE_NAME` = 'sw_transition'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.033269;i:4;a:0:{}i:5;i:9095984;}i:327;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_transition' AND `kcu`.`TABLE_NAME` = 'sw_transition'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.034421;i:4;a:0:{}i:5;i:9097248;}i:329;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_workflow`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.034467;i:4;a:0:{}i:5;i:9095952;}i:330;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `sw_workflow`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.035502;i:4;a:0:{}i:5;i:9098496;}i:332;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_workflow`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.035537;i:4;a:0:{}i:5;i:9099512;}i:333;a:6:{i:0;s:31:"SHOW CREATE TABLE `sw_workflow`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.036412;i:4;a:0:{}i:5;i:9100512;}i:335;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_workflow' AND `kcu`.`TABLE_NAME` = 'sw_workflow'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.036521;i:4;a:0:{}i:5;i:9100904;}i:336;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_workflow' AND `kcu`.`TABLE_NAME` = 'sw_workflow'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.037277;i:4;a:0:{}i:5;i:9102168;}i:338;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `test`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.037329;i:4;a:0:{}i:5;i:9101504;}i:339;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `test`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.038491;i:4;a:0:{}i:5;i:9103984;}i:341;a:6:{i:0;s:24:"SHOW CREATE TABLE `test`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.038524;i:4;a:0:{}i:5;i:9105000;}i:342;a:6:{i:0;s:24:"SHOW CREATE TABLE `test`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.039093;i:4;a:0:{}i:5;i:9105896;}i:344;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'test' AND `kcu`.`TABLE_NAME` = 'test'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.039154;i:4;a:0:{}i:5;i:9106432;}i:345;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'test' AND `kcu`.`TABLE_NAME` = 'test'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.040284;i:4;a:0:{}i:5;i:9107696;}i:347;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.040322;i:4;a:0:{}i:5;i:9106416;}i:348;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `user_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.04146;i:4;a:0:{}i:5;i:9112592;}i:350;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.041491;i:4;a:0:{}i:5;i:9113616;}i:351;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.042105;i:4;a:0:{}i:5;i:9114936;}i:353;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_permission' AND `kcu`.`TABLE_NAME` = 'user_permission'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.042185;i:4;a:0:{}i:5;i:9113128;}i:354;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_permission' AND `kcu`.`TABLE_NAME` = 'user_permission'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.043445;i:4;a:0:{}i:5;i:9115072;}i:356;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.043488;i:4;a:0:{}i:5;i:9113984;}i:357;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.04464;i:4;a:0:{}i:5;i:9119976;}i:359;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.044682;i:4;a:0:{}i:5;i:9120992;}i:360;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.045322;i:4;a:0:{}i:5;i:9122432;}i:362;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.045402;i:4;a:0:{}i:5;i:9120504;}i:363;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.046714;i:4;a:0:{}i:5;i:9122984;}i:365;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.046875;i:4;a:0:{}i:5;i:9125968;}i:366;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `active_record`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.05089;i:4;a:0:{}i:5;i:9152400;}i:368;a:6:{i:0;s:28:"SHOW CREATE TABLE `afspraak`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.051065;i:4;a:0:{}i:5;i:9132976;}i:369;a:6:{i:0;s:28:"SHOW CREATE TABLE `afspraak`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.051623;i:4;a:0:{}i:5;i:9134248;}i:371;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.055132;i:4;a:0:{}i:5;i:9135456;}i:372;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.055837;i:4;a:0:{}i:5;i:9136584;}i:374;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_error`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.055981;i:4;a:0:{}i:5;i:9137776;}i:375;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_error`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.056308;i:4;a:0:{}i:5;i:9139096;}i:377;a:6:{i:0;s:36:"SHOW CREATE TABLE `audit_javascript`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.056411;i:4;a:0:{}i:5;i:9139744;}i:378;a:6:{i:0;s:36:"SHOW CREATE TABLE `audit_javascript`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.056713;i:4;a:0:{}i:5;i:9140944;}i:380;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_mail`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.056811;i:4;a:0:{}i:5;i:9141712;}i:381;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_mail`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.05716;i:4;a:0:{}i:5;i:9143160;}i:383;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_trail`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.057257;i:4;a:0:{}i:5;i:9143976;}i:384;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_trail`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.057826;i:4;a:0:{}i:5;i:9145424;}i:386;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.057925;i:4;a:0:{}i:5;i:9146320;}i:387;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.058464;i:4;a:0:{}i:5;i:9148536;}i:389;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.058554;i:4;a:0:{}i:5;i:9148208;}i:390;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.059084;i:4;a:0:{}i:5;i:9150424;}i:392;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.059264;i:4;a:0:{}i:5;i:9149792;}i:393;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.059766;i:4;a:0:{}i:5;i:9152008;}i:395;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.059858;i:4;a:0:{}i:5;i:9151680;}i:396;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.060344;i:4;a:0:{}i:5;i:9153896;}i:398;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.060449;i:4;a:0:{}i:5;i:9153664;}i:399;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.061047;i:4;a:0:{}i:5;i:9155376;}i:401;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.061135;i:4;a:0:{}i:5;i:9155248;}i:402;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.061756;i:4;a:0:{}i:5;i:9156960;}i:404;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.061846;i:4;a:0:{}i:5;i:9156856;}i:405;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.062243;i:4;a:0:{}i:5;i:9158568;}i:407;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.062337;i:4;a:0:{}i:5;i:9159240;}i:408;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.062742;i:4;a:0:{}i:5;i:9160952;}i:410;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.062837;i:4;a:0:{}i:5;i:9160824;}i:411;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.063171;i:4;a:0:{}i:5;i:9162536;}i:413;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.063286;i:4;a:0:{}i:5;i:9162424;}i:414;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.063625;i:4;a:0:{}i:5;i:9164136;}i:416;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.063735;i:4;a:0:{}i:5;i:9165128;}i:417;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.064121;i:4;a:0:{}i:5;i:9166584;}i:419;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.064207;i:4;a:0:{}i:5;i:9167072;}i:420;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.064494;i:4;a:0:{}i:5;i:9168528;}i:422;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.065036;i:4;a:0:{}i:5;i:9169720;}i:423;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.065654;i:4;a:0:{}i:5;i:9171304;}i:425;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.065779;i:4;a:0:{}i:5;i:9171296;}i:426;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.066473;i:4;a:0:{}i:5;i:9172880;}i:428;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.066559;i:4;a:0:{}i:5;i:9173192;}i:429;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.066921;i:4;a:0:{}i:5;i:9174768;}i:431;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.067006;i:4;a:0:{}i:5;i:9174768;}i:432;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.067371;i:4;a:0:{}i:5;i:9176344;}i:434;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.067464;i:4;a:0:{}i:5;i:9176320;}i:435;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.067822;i:4;a:0:{}i:5;i:9177768;}i:437;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.067913;i:4;a:0:{}i:5;i:9178328;}i:438;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.068238;i:4;a:0:{}i:5;i:9179912;}i:440;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.06835;i:4;a:0:{}i:5;i:9179928;}i:441;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.068794;i:4;a:0:{}i:5;i:9181512;}i:443;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.069002;i:4;a:0:{}i:5;i:9181936;}i:444;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.069408;i:4;a:0:{}i:5;i:9183640;}i:446;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.069544;i:4;a:0:{}i:5;i:9183512;}i:447;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.069893;i:4;a:0:{}i:5;i:9185216;}i:449;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.069994;i:4;a:0:{}i:5;i:9185528;}i:450;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.07041;i:4;a:0:{}i:5;i:9187112;}i:452;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.070546;i:4;a:0:{}i:5;i:9187768;}i:453;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.070962;i:4;a:0:{}i:5;i:9189352;}i:455;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.071052;i:4;a:0:{}i:5;i:9189336;}i:456;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.071426;i:4;a:0:{}i:5;i:9191040;}i:458;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.071512;i:4;a:0:{}i:5;i:9190864;}i:459;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.07184;i:4;a:0:{}i:5;i:9192568;}i:461;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.071963;i:4;a:0:{}i:5;i:9192832;}i:462;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.072347;i:4;a:0:{}i:5;i:9194408;}i:464;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.072433;i:4;a:0:{}i:5;i:9194408;}i:465;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.072754;i:4;a:0:{}i:5;i:9195984;}i:467;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.072871;i:4;a:0:{}i:5;i:9196376;}i:468;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.073238;i:4;a:0:{}i:5;i:9198080;}i:470;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.073324;i:4;a:0:{}i:5;i:9197912;}i:471;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.07372;i:4;a:0:{}i:5;i:9199616;}i:473;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.073821;i:4;a:0:{}i:5;i:9199840;}i:474;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.074348;i:4;a:0:{}i:5;i:9201544;}i:476;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.074445;i:4;a:0:{}i:5;i:9201920;}i:477;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.0748;i:4;a:0:{}i:5;i:9203248;}i:479;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.074895;i:4;a:0:{}i:5;i:9203888;}i:480;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.075257;i:4;a:0:{}i:5;i:9205216;}i:482;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.075344;i:4;a:0:{}i:5;i:9206280;}i:483;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.075759;i:4;a:0:{}i:5;i:9207608;}i:485;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.075863;i:4;a:0:{}i:5;i:9208296;}i:486;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.076226;i:4;a:0:{}i:5;i:9209752;}i:488;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.076311;i:4;a:0:{}i:5;i:9209856;}i:489;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.076706;i:4;a:0:{}i:5;i:9211312;}i:491;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.076792;i:4;a:0:{}i:5;i:9211392;}i:492;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.077152;i:4;a:0:{}i:5;i:9212960;}i:494;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.077238;i:4;a:0:{}i:5;i:9212888;}i:495;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.077594;i:4;a:0:{}i:5;i:9214456;}i:497;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.077697;i:4;a:0:{}i:5;i:9214864;}i:498;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.078142;i:4;a:0:{}i:5;i:9216576;}i:500;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.078224;i:4;a:0:{}i:5;i:9216464;}i:501;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.078661;i:4;a:0:{}i:5;i:9218176;}i:503;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.078759;i:4;a:0:{}i:5;i:9219456;}i:504;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.079071;i:4;a:0:{}i:5;i:9221408;}i:506;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.0796;i:4;a:0:{}i:5;i:9221376;}i:507;a:6:{i:0;s:35:"SHOW CREATE TABLE `user_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.080341;i:4;a:0:{}i:5;i:9222696;}i:509;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.080489;i:4;a:0:{}i:5;i:9222896;}i:510;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.081114;i:4;a:0:{}i:5;i:9224336;}i:512;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.081201;i:4;a:0:{}i:5;i:9224408;}i:513;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.081564;i:4;a:0:{}i:5;i:9234040;}i:515;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.081653;i:4;a:0:{}i:5;i:9234984;}i:516;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082016;i:4;a:0:{}i:5;i:9237200;}i:518;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082057;i:4;a:0:{}i:5;i:9235696;}i:519;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082425;i:4;a:0:{}i:5;i:9237408;}i:521;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082463;i:4;a:0:{}i:5;i:9236408;}i:522;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082816;i:4;a:0:{}i:5;i:9238120;}i:524;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.082854;i:4;a:0:{}i:5;i:9237120;}i:525;a:6:{i:0;s:39:"SHOW CREATE TABLE `document_permission`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.083185;i:4;a:0:{}i:5;i:9238576;}i:527;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.083221;i:4;a:0:{}i:5;i:9237832;}i:528;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.083526;i:4;a:0:{}i:5;i:9239416;}i:530;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.083698;i:4;a:0:{}i:5;i:9238544;}i:531;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.084321;i:4;a:0:{}i:5;i:9240120;}i:533;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.084359;i:4;a:0:{}i:5;i:9239256;}i:534;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.084782;i:4;a:0:{}i:5;i:9240840;}i:536;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.084818;i:4;a:0:{}i:5;i:9239968;}i:537;a:6:{i:0;s:33:"SHOW CREATE TABLE `functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.085207;i:4;a:0:{}i:5;i:9241672;}i:539;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.085244;i:4;a:0:{}i:5;i:9240680;}i:540;a:6:{i:0;s:38:"SHOW CREATE TABLE `functionality_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.085563;i:4;a:0:{}i:5;i:9242264;}i:542;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.085602;i:4;a:0:{}i:5;i:9241392;}i:543;a:6:{i:0;s:32:"SHOW CREATE TABLE `gedetineerde`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.085971;i:4;a:0:{}i:5;i:9243096;}i:545;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.086034;i:4;a:0:{}i:5;i:9242104;}i:546;a:6:{i:0;s:33:"SHOW CREATE TABLE `instance_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.086323;i:4;a:0:{}i:5;i:9243680;}i:548;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.086362;i:4;a:0:{}i:5;i:9242808;}i:549;a:6:{i:0;s:29:"SHOW CREATE TABLE `instantie`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.086714;i:4;a:0:{}i:5;i:9244512;}i:551;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.086768;i:4;a:0:{}i:5;i:9243520;}i:552;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.087034;i:4;a:0:{}i:5;i:9244848;}i:554;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.087362;i:4;a:0:{}i:5;i:9245056;}i:555;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.08782;i:4;a:0:{}i:5;i:9246512;}i:557;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.087861;i:4;a:0:{}i:5;i:9245760;}i:558;a:6:{i:0;s:24:"SHOW CREATE TABLE `role`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.088073;i:4;a:0:{}i:5;i:9247328;}i:560;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.088115;i:4;a:0:{}i:5;i:9246472;}i:561;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.088431;i:4;a:0:{}i:5;i:9248184;}i:563;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.088492;i:4;a:0:{}i:5;i:9247176;}i:564;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.088846;i:4;a:0:{}i:5;i:9248616;}i:566;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.089049;i:4;a:0:{}i:5;i:9255568;}i:567;a:6:{i:0;s:40:"SHOW CREATE TABLE `notification_trigger`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1758641621.089393;i:4;a:0:{}i:5;i:9256896;}i:575;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.96063184738159, `memory_max`=10380936 WHERE `id`=5129";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1758641621.636959;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:10156704;}i:576;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.96063184738159, `memory_max`=10380936 WHERE `id`=5129";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1758641621.640951;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:10158104;}}}";s:5:"event";s:9022:"a:51:{i:0;a:5:{s:4:"time";d:1758641620.728223;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1758641620.755891;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1758641620.785291;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:1758641620.785337;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:1758641620.796039;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:1758641620.828992;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1758641620.837898;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:7;a:5:{s:4:"time";d:1758641620.837962;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:8;a:5:{s:4:"time";d:1758641620.843762;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:9;a:5:{s:4:"time";d:1758641620.853239;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:10;a:5:{s:4:"time";d:1758641620.853267;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:1758641620.853292;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:14:"yii\gii\Module";}i:12;a:5:{s:4:"time";d:1758641620.872481;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"yii\gii\controllers\DefaultController";}i:13;a:5:{s:4:"time";d:1758641620.874551;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:14;a:5:{s:4:"time";d:1758641620.884067;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:34:"yii\gii\generators\model\Generator";}i:15;a:5:{s:4:"time";d:1758641620.893582;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:34:"yii\gii\generators\model\Generator";}i:16;a:5:{s:4:"time";d:1758641621.046776;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\db\ActiveRecord";}i:17;a:5:{s:4:"time";d:1758641621.158364;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:18;a:5:{s:4:"time";d:1758641621.42747;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:19;a:5:{s:4:"time";d:1758641621.428415;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:20;a:5:{s:4:"time";d:1758641621.442225;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:21;a:5:{s:4:"time";d:1758641621.442572;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:22;a:5:{s:4:"time";d:1758641621.470547;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:23;a:5:{s:4:"time";d:1758641621.472035;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:24;a:5:{s:4:"time";d:1758641621.622831;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:25;a:5:{s:4:"time";d:1758641621.623494;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:26;a:5:{s:4:"time";d:1758641621.624839;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:27;a:5:{s:4:"time";d:1758641621.624914;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:28;a:5:{s:4:"time";d:1758641621.625422;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:29;a:5:{s:4:"time";d:1758641621.627193;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"yii\widgets\ContentDecorator";}i:30;a:5:{s:4:"time";d:1758641621.627579;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"yii\widgets\ContentDecorator";}i:31;a:5:{s:4:"time";d:1758641621.627866;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:32;a:5:{s:4:"time";d:1758641621.630828;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:33;a:5:{s:4:"time";d:1758641621.631082;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:34;a:5:{s:4:"time";d:1758641621.632852;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Menu";}i:35;a:5:{s:4:"time";d:1758641621.632875;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Menu";}i:36;a:5:{s:4:"time";d:1758641621.633076;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Menu";}i:37;a:5:{s:4:"time";d:1758641621.635704;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:38;a:5:{s:4:"time";d:1758641621.636121;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:39;a:5:{s:4:"time";d:1758641621.636562;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:40;a:5:{s:4:"time";d:1758641621.636587;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"yii\widgets\ContentDecorator";}i:41;a:5:{s:4:"time";d:1758641621.63661;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:42;a:5:{s:4:"time";d:1758641621.636628;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"yii\gii\controllers\DefaultController";}i:43;a:5:{s:4:"time";d:1758641621.63664;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:14:"yii\gii\Module";}i:44;a:5:{s:4:"time";d:1758641621.63665;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:45;a:5:{s:4:"time";d:1758641621.636725;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:46;a:5:{s:4:"time";d:1758641621.640982;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:47;a:5:{s:4:"time";d:1758641621.64099;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:48;a:5:{s:4:"time";d:1758641621.640997;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:49;a:5:{s:4:"time";d:1758641621.641442;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:50;a:5:{s:4:"time";d:1758641621.641679;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:1758641620.676042;s:3:"end";d:1758641621.646095;s:6:"memory";i:10380936;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:314:"a:3:{s:8:"messages";a:1:{i:36;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1758641620.829029;i:4;a:0:{}i:5;i:7326832;}}s:5:"route";s:16:"gii/default/view";s:6:"action";s:51:"yii\gii\controllers\DefaultController::actionView()";}";s:7:"request";s:13665:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:20:{s:12:"content-type";s:33:"application/x-www-form-urlencoded";s:14:"content-length";s:4:"1050";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:6:"origin";s:21:"http://localhost:8005";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36";s:7:"referer";s:74:"http://localhost:8005/backoffice/index.php?r=gii%2Fdefault%2Fview&id=model";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:713:"_identity-frontend=1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=true; advanced-backend-fmz=f2ogsas3vk17thstbc5sgrqolh; _csrf-backend=69f717306beac3df25c49dc083734356a4923102181c8d61afd6fc59d4f3e26ca%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22zTn06yqYNPkSe3ceADx2BCG79pXI9ZPb%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";s:13:"cache-control";s:9:"max-age=0";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68d2bdd4c49e8";s:16:"X-Debug-Duration";s:3:"966";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=68d2bdd4c49e8";s:10:"Set-Cookie";s:311:"_identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; expires=Thu, 23 Oct 2025 15:33:40 GMT; Max-Age=2591999; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:16:"gii/default/view";s:6:"action";s:51:"yii\gii\controllers\DefaultController::actionView()";s:12:"actionParams";a:1:{s:2:"id";s:5:"model";}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:33:"application/x-www-form-urlencoded";s:3:"Raw";s:1050:"_csrf-backend=PXbm4ZIIRytCoTdpBPXupDmESgQyg_hs8f6LUstIdyFHIojRpHE2cgzxXDphxo3BeMAyNnDAv1vIjtMb8hInQw%3D%3D&Generator%5Bdb%5D=db&Generator%5BuseTablePrefix%5D=0&Generator%5BuseSchemaName%5D=0&Generator%5BuseSchemaName%5D=1&Generator%5BtableName%5D=notification_trigger&Generator%5BstandardizeCapitals%5D=0&Generator%5Bsingularize%5D=0&Generator%5BmodelClass%5D=NotificationTrigger&Generator%5Bns%5D=common%5Cmodels&Generator%5BbaseClass%5D=yii%5Cdb%5CActiveRecord&Generator%5BgenerateRelations%5D=all&Generator%5BgenerateJunctionRelationMode%5D=table&Generator%5BgenerateRelationsFromCurrentSchema%5D=0&Generator%5BgenerateRelationsFromCurrentSchema%5D=1&Generator%5BgenerateRelationNameFromDestinationTable%5D=0&Generator%5BuseClassConstant%5D=0&Generator%5BuseClassConstant%5D=1&Generator%5BgenerateLabelsFromComments%5D=0&Generator%5BgenerateQuery%5D=0&Generator%5BqueryNs%5D=common%5Cmodels&Generator%5BqueryBaseClass%5D=yii%5Cdb%5CActiveQuery&Generator%5BenableI18N%5D=0&Generator%5BmessageCategory%5D=app&Generator%5Btemplate%5D=default&preview=";s:7:"Decoded";a:3:{s:13:"_csrf-backend";s:88:"PXbm4ZIIRytCoTdpBPXupDmESgQyg_hs8f6LUstIdyFHIojRpHE2cgzxXDphxo3BeMAyNnDAv1vIjtMb8hInQw==";s:9:"Generator";a:21:{s:2:"db";s:2:"db";s:14:"useTablePrefix";s:1:"0";s:13:"useSchemaName";s:1:"1";s:9:"tableName";s:20:"notification_trigger";s:19:"standardizeCapitals";s:1:"0";s:11:"singularize";s:1:"0";s:10:"modelClass";s:19:"NotificationTrigger";s:2:"ns";s:13:"common\models";s:9:"baseClass";s:19:"yii\db\ActiveRecord";s:17:"generateRelations";s:3:"all";s:28:"generateJunctionRelationMode";s:5:"table";s:34:"generateRelationsFromCurrentSchema";s:1:"1";s:40:"generateRelationNameFromDestinationTable";s:1:"0";s:16:"useClassConstant";s:1:"1";s:26:"generateLabelsFromComments";s:1:"0";s:13:"generateQuery";s:1:"0";s:7:"queryNs";s:13:"common\models";s:14:"queryBaseClass";s:18:"yii\db\ActiveQuery";s:10:"enableI18N";s:1:"0";s:15:"messageCategory";s:3:"app";s:8:"template";s:7:"default";}s:7:"preview";s:0:"";}}s:6:"SERVER";a:108:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-1e0e8492-409f-43c2-a88f-10d1fcf70959";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:582:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\GitHub CLI\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:53:"/backoffice/index.php?r=gii%2Fdefault%2Fview&id=model";s:14:"REQUEST_METHOD";s:4:"POST";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"55998";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:31:"r=gii%2Fdefault%2Fview&id=model";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:14:"CONTENT_LENGTH";s:4:"1050";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:11:"HTTP_ORIGIN";s:21:"http://localhost:8005";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36";s:12:"HTTP_REFERER";s:74:"http://localhost:8005/backoffice/index.php?r=gii%2Fdefault%2Fview&id=model";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:713:"_identity-frontend=1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=true; advanced-backend-fmz=f2ogsas3vk17thstbc5sgrqolh; _csrf-backend=69f717306beac3df25c49dc083734356a4923102181c8d61afd6fc59d4f3e26ca%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22zTn06yqYNPkSe3ceADx2BCG79pXI9ZPb%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:17:"HTTP_CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:19:"HTTP_CONTENT_LENGTH";s:4:"1050";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:1758641620.65954;s:12:"REQUEST_TIME";i:1758641620;}s:3:"GET";a:2:{s:1:"r";s:16:"gii/default/view";s:2:"id";s:5:"model";}s:4:"POST";a:3:{s:13:"_csrf-backend";s:88:"PXbm4ZIIRytCoTdpBPXupDmESgQyg_hs8f6LUstIdyFHIojRpHE2cgzxXDphxo3BeMAyNnDAv1vIjtMb8hInQw==";s:9:"Generator";a:21:{s:2:"db";s:2:"db";s:14:"useTablePrefix";s:1:"0";s:13:"useSchemaName";s:1:"1";s:9:"tableName";s:20:"notification_trigger";s:19:"standardizeCapitals";s:1:"0";s:11:"singularize";s:1:"0";s:10:"modelClass";s:19:"NotificationTrigger";s:2:"ns";s:13:"common\models";s:9:"baseClass";s:19:"yii\db\ActiveRecord";s:17:"generateRelations";s:3:"all";s:28:"generateJunctionRelationMode";s:5:"table";s:34:"generateRelationsFromCurrentSchema";s:1:"1";s:40:"generateRelationNameFromDestinationTable";s:1:"0";s:16:"useClassConstant";s:1:"1";s:26:"generateLabelsFromComments";s:1:"0";s:13:"generateQuery";s:1:"0";s:7:"queryNs";s:13:"common\models";s:14:"queryBaseClass";s:18:"yii\db\ActiveQuery";s:10:"enableI18N";s:1:"0";s:15:"messageCategory";s:3:"app";s:8:"template";s:7:"default";}s:7:"preview";s:0:"";}s:6:"COOKIE";a:5:{s:18:"_identity-frontend";s:158:"1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo",2592000]";}";s:17:"_identity-backend";s:157:"39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:16:"sidebar-collapse";s:4:"true";s:20:"advanced-backend-fmz";s:26:"f2ogsas3vk17thstbc5sgrqolh";s:13:"_csrf-backend";s:139:"69f717306beac3df25c49dc083734356a4923102181c8d61afd6fc59d4f3e26ca:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"zTn06yqYNPkSe3ceADx2BCG79pXI9ZPb";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1741788198";s:10:"updated_at";s:10:"1749661338";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_1741788198'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:2060:"a:5:{s:30:"yii\validators\ValidationAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:17:"yii.validation.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:50:"C:\Web\Reclassering/vendor/bower-asset/jquery/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\8bc86ca8";s:7:"baseUrl";s:27:"/backoffice/assets/8bc86ca8";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:27:"yii\widgets\ActiveFormAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:17:"yii.activeForm.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\gii\GiiAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src/assets";s:8:"basePath";s:46:"C:\Web\Reclassering\backend\web\assets\59215b8";s:7:"baseUrl";s:26:"/backoffice/assets/59215b8";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:2:{i:0;s:20:"js/bs4-native.min.js";i:1;s:9:"js/gii.js";}s:3:"css";a:1:{i:0;s:12:"css/main.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68d2bdd4c49e8";s:3:"url";s:74:"http://localhost:8005/backoffice/index.php?r=gii%2Fdefault%2Fview&id=model";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";d:1758641620.65954;s:10:"statusCode";i:200;s:8:"sqlCount";i:184;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10380936;s:14:"processingTime";d:0.9678668975830078;}s:10:"exceptions";a:0:{}}